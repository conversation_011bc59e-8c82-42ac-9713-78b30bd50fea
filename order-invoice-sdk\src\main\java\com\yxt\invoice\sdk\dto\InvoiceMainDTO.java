package com.yxt.invoice.sdk.dto;

import com.yxt.order.types.invoice.enums.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class InvoiceMainDTO {

    /**
     * 发票ID
     */
    private Long id;

    /**
     * 分公司编码
     */
    private String companyCode;

    /**
     * 分公司名称
     */
    private String companyName;

    /**
     * 所属机构编码
     */
    private String organizationCode;

    /**
     * 所属机构名称
     */
    private String organizationName;

    /**
     * 开票单号
     */
    private String invoiceMainNo;

    /**
     * 平台编码
     */
    private String thirdPlatformCode;

    /**
     * 第三方平台订单号
     */
    private String thirdOrderNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * pos销售单号
     */
    private String posNo;


    /**
     * 会员编号
     */
    private String userId;

    /**
     * 商户编码
     */
    private String merCode;

    /**
     * 交易场景 online:线上交易, offline:线下交易
     */
    private String transactionChannel;

    /**
     * 发票供应方编码
     */
    private String providerCode;

    /**
     * 发票代码（税务云回调写入）
     */
    private String invoiceCode;

    /**
     * 发票号码（税务云回调写入）
     */
    private String invoiceNo;

    /**
     * 蓝票:Tax_Invoice 红票:Credit_Note
     */
    private InvoiceRedBlueTypeEnum invoiceRedBlueType;

    /**
     * 红冲对应原发票号,红票必填
     */
    private String redInvoiceMainNo;

    /**
     * 红冲原因
     * 01: 开票有误
     * 02: 销货退回
     * 03: 服务中止
     * 04: 销售折让
     */
    private String redInvoiceReason;

    /**
     *  备注
     */
    private String notes;

    /**
     * 发票类型
     */
    private InvoiceTypeEnum invoiceType;

    /**
     * 发票状态
     */
    private InvoiceStatusEnum invoiceStatus;

    /**
     * 同步状态
     */
    private InvoiceSyncStatusEnum syncStatus;

    /**
     * 实付金额
     */
    private BigDecimal actualPayAmount;

    /**
     * 配送费
     */
    private BigDecimal deliveryAmount;

    /**
     * 配送方式 PlatformFulfillment:平台配送 MerchantFulfillment:商家自配
     */
    private String deliveryType;

    /**
     * 发票金额
     */
    private BigDecimal invoiceAmount;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 价税合计
     */
    private BigDecimal priceTaxAmount;

    /**
     * 拆票标记 SPLIT-拆 NOT-不拆
     */
    private String splitBill;

    /**
     * 订单创单时间
     */
    private LocalDateTime orderCreated;

    /**
     * 电子发票PDF地址
     */
    private String pdfUrl;

    /**
     * 失败原因
     */
    private String invoiceErrMsg;

    /**
     * 申请开票时间
     */
    private LocalDateTime applyTime;

    /**
     * 开票人
     */
    private String operator;

    /**
     * 收款人
     */
    private String payee;

    /**
     * 复核人
     */
    private String reviewed;

    /**
     * 申请渠道 一心到家-YXDJ 心云-XY 海典H2-H2POS
     */
    private String applyChannel;

    /**
     * 开票主体
     */
    private String sellerNumber;

    /**
     * 开票主体名称
     */
    private String sellerName;

    /**
     * 开票纳税人识别号
     */
    private String sellerTin;

    /**
     * 开票主体地址
     */
    private String sellerAddress;

    /**
     * 开票主体电话
     */
    private String sellerPhone;

    /**
     * 开票主体银行
     */
    private String sellerBank;

    /**
     * 开票主体银行账户
     */
    private String sellerBankAccount;

    /**
     * 购方类型
     */
    private InvoiceBuyerPartyTypeEnum buyerPartyType;

    /**
     * 购方名称
     */
    private String buyerName;

    /**
     * 购方税号（个人身份证/单位纳税人识别号）
     */
    private String buyerTin;

    /**
     * 购方地址
     */
    private String buyerAddress;

    /**
     * 购方电话
     */
    private String buyerPhone;

    /**
     * 购方银行
     */
    private String buyerBank;

    /**
     * 购方银行账户
     */
    private String buyerBankAccount;

    /**
     * 购方邮箱
     */
    private String buyerEmail;

    /**
     * 购方手机号
     */
    private String buyerMobile;

    /**
     * 显示购方银行账户 SHOW-显示 HIDE-不显示
     */
    private String showBuyerBankAccount;

    /**
     * 是否起效 1-起效 -1-未起效
     */
    private Long isValid;

    /**
     * 平台创建时间
     */
    private LocalDateTime created;

    /**
     * 平台更新时间
     */
    private LocalDateTime updated;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 系统创建时间
     */
    private LocalDateTime sysCreateTime;

    /**
     * 系统更新时间
     */
    private LocalDateTime sysUpdateTime;

    /**
     * 数据版本，每次update+1
     */
    private Long version;


}
