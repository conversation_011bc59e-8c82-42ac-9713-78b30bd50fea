package com.yxt.invoice.interfaces.mq.es.es_invoice_main.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class FlashOfflineOrderManageDataReq {
  @NotNull(message = "startDate can not null")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date startDate;

  @NotNull(message = "endDate can not null")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date endDate;

  /**
   * 单号列表,这里模糊掉是正单还是退单,交给处理器决定是否需要处理
   */
  private List<String> noList;

  private String monitorKey;

}
