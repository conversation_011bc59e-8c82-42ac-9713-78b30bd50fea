package com.yxt.invoice.infrastructure.db.es.operate;

import com.yxt.common.logic.es.AbstractEsOperate;
import com.yxt.invoice.infrastructure.db.es.es_invoice_main.doc.EsInvoiceMain;
import com.yxt.invoice.infrastructure.db.es.es_invoice_main.mapper.EsInvoiceMainMapper;
import com.yxt.invoice.infrastructure.db.es.es_invoice_main.model.EsInvoiceMainModel;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2024/12/10 15:03
 */
@Component
@Slf4j
public class EsInvoiceMainModelOperate extends AbstractEsOperate<EsInvoiceMainModel> {

  @Resource
  private EsInvoiceMainMapper esInvoiceMainMapper;

  public EsInvoiceMainModelOperate() {
    super(EsInvoiceMainModel.class);
  }

  @Override
  protected Boolean exec() {

    if (Type.DELETE.equals(getType())) {
      return delete(getT());
    } else if (Type.SAVE.equals(getType())) {
      return save(getT());
    }

    return false;
  }

  private Boolean delete(EsInvoiceMainModel esInvoiceMainModel) {
    return esInvoiceMainMapper.deleteById(esInvoiceMainModel.defineId()) > 0;
  }

  private Boolean save(EsInvoiceMainModel esInvoiceMainModel) {
    EsInvoiceMain esMemberOrder = esInvoiceMainModel.create();
    LambdaEsQueryWrapper<EsInvoiceMain> memberOrderQuery = new LambdaEsQueryWrapper<>();
    memberOrderQuery.eq(EsInvoiceMain::getInvoiceMainNo, esInvoiceMainModel.defineId());
    Long count = esInvoiceMainMapper.selectCount(memberOrderQuery);
    if (count > 0) {
      return esInvoiceMainMapper.updateById(esMemberOrder) > 0;
    } else {
      return esInvoiceMainMapper.insert(esMemberOrder) > 0;

    }
  }
}
