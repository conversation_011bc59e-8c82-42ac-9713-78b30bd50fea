package com.yxt.invoice.application.result;

/**
 * 发票申请结果
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public class InvoiceApplicationResult {

    /**
     * 发票ID
     */
    private Long invoiceId;

    /**
     * 开票单号
     */
    private String invoiceMainNo;

    /**
     * 发票状态
     */
    private String invoiceStatus;

    /**
     * 发票状态描述
     */
    private String invoiceStatusDesc;

    /**
     * 申请是否成功
     */
    private Boolean success;

    /**
     * 消息
     */
    private String message;

    /**
     * 私有构造函数
     */
    private InvoiceApplicationResult() {
    }

    /**
     * 成功结果
     */
    public static InvoiceApplicationResult success(Long invoiceId, String invoiceMainNo, 
                                                 String invoiceStatus, String invoiceStatusDesc) {
        InvoiceApplicationResult result = new InvoiceApplicationResult();
        result.invoiceId = invoiceId;
        result.invoiceMainNo = invoiceMainNo;
        result.invoiceStatus = invoiceStatus;
        result.invoiceStatusDesc = invoiceStatusDesc;
        result.success = true;
        result.message = "申请成功";
        return result;
    }

    /**
     * 失败结果
     */
    public static InvoiceApplicationResult failure(String message) {
        InvoiceApplicationResult result = new InvoiceApplicationResult();
        result.success = false;
        result.message = message;
        return result;
    }

    // Getters
    public Long getInvoiceId() {
        return invoiceId;
    }

    public String getInvoiceMainNo() {
        return invoiceMainNo;
    }

    public String getInvoiceStatus() {
        return invoiceStatus;
    }

    public String getInvoiceStatusDesc() {
        return invoiceStatusDesc;
    }

    public Boolean getSuccess() {
        return success;
    }

    public String getMessage() {
        return message;
    }
}
