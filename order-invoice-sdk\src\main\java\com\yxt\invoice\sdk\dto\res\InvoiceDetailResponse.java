package com.yxt.invoice.sdk.dto.res;

import com.yxt.invoice.sdk.dto.InvoiceDetailDTO;
import com.yxt.invoice.sdk.dto.InvoiceMainDTO;
import com.yxt.lang.dto.api.ResponseDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 发票详情响应
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Data
public class InvoiceDetailResponse extends ResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private InvoiceMainDTO invoiceMain;

    /**
     * 发票明细列表
     */
    private List<InvoiceDetailDTO> details;

    public InvoiceDetailResponse(InvoiceMainDTO invoiceMainDTO, List<InvoiceDetailDTO> collect) {

        this.invoiceMain = invoiceMainDTO;
        this.details = collect;
    }
}
