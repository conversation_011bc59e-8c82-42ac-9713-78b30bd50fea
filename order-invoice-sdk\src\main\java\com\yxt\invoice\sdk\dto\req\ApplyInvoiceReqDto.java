package com.yxt.invoice.sdk.dto.req;

import com.yxt.invoice.sdk.dto.InvoiceDetailDTO;
import com.yxt.invoice.sdk.dto.InvoiceMainDTO;
import com.yxt.lang.dto.api.MiddleRequestBase;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 发票申请请求
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Data
public class ApplyInvoiceReqDto extends MiddleRequestBase implements Serializable {

    private static final long serialVersionUID = 1L;
    @NotEmpty(message = "发票信息不能为空")
    @Valid
    private InvoiceMainDTO invoiceMain;

    /**
     * 发票明细列表
     */
    @NotEmpty(message = "发票明细不能为空")
    @Valid
    private List<InvoiceDetailDTO> details;




}
