package com.yxt.invoice.domain.factory;

import com.yxt.invoice.domain.command.ApplyInvoiceCommand;
import com.yxt.invoice.domain.command.RedCreditInvoiceCommand;
import com.yxt.invoice.domain.external.IdService;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.order.types.invoice.enums.InvoiceRedBlueTypeEnum;
import com.yxt.order.types.offline.OfflineUserId;
import com.yxt.order.types.utils.InvoiceMainNoCreate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 发票工厂
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Component
public class InvoiceAggregateFactory {


    @Resource
    private IdService idService;

    public InvoiceAggregate createAggravate(ApplyInvoiceCommand command) {
        // 生成内部单号
        String invoiceNo = idService.generateId(IdService.IdType.INVOICE_MAIN_NO);

        command.getInvoiceMain().setInvoiceMainNo(invoiceNo);
        command.getDetails().forEach(d->{
             d.setInvoiceMainNo(invoiceNo);
             d.setInvoiceDetailNo(invoiceNo + "_" + d.getRowNo());
        });
        InvoiceAggregate rebuild = InvoiceAggregate.rebuild(command.getInvoiceMain(), command.getDetails());
        rebuild.invoiceApply();

        return rebuild;
    }

    public InvoiceAggregate redCreditAggregate(InvoiceAggregate aggregate, RedCreditInvoiceCommand command) {
        InvoiceAggregate redInvoiceAggregate = aggregate.clone();
        String distributeId = idService.generateId(IdService.IdType.INVOICE_MAIN_NO);
        Date now = new Date();
        OfflineUserId userId =aggregate.isMemberOrder() ? OfflineUserId.userId(aggregate.getInvoiceMain().getUserId()) : null;
        // 生成内部单号
        String InvoiceMainNo = InvoiceMainNoCreate.create(InvoiceRedBlueTypeEnum.CREDIT_NOTE,distributeId, userId, now);
        redInvoiceAggregate.redCreditApply(InvoiceMainNo,command.getOperatorUserId(),command.getRedCreditReason(),command.getNotes(),command.getApplyTime());

        return redInvoiceAggregate;


    }
}
