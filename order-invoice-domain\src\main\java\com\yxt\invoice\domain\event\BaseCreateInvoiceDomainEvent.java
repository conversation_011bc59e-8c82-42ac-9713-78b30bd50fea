package com.yxt.invoice.domain.event;

import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.lang.dto.OperateContext;
import com.yxt.order.types.invoice.enums.InvoiceRedBlueTypeEnum;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

/**
 * 发票模型事件
 *
 * <AUTHOR> (moatkon)
 * @date 2024年04月03日 18:29
 * @email: <EMAIL>
 */
@Slf4j
@NoArgsConstructor
public class BaseCreateInvoiceDomainEvent<T> extends BaseInvoiceDomainEvent<T> {


  protected BaseCreateInvoiceDomainEvent(InvoiceAggregate aggregate,
                                         OperateContext operateContext, String type, T data) {
    super(aggregate.getInvoiceMain().getOrderNo().getOrderNo(), operateContext,
              type, data);
  }

  @Getter
  @Setter
  @ToString(callSuper = true)
  public static class BaseData {

    private String merCode;
    private Long orderNo;
    private String invoiceMainNo;
    private InvoiceRedBlueTypeEnum invoiceRedBlueTypeEnum;

    protected void convert(InvoiceAggregate aggregate) {
      InvoiceMain invoice = aggregate.getInvoiceMain();
      this.merCode = invoice.getMerCode();
      this.orderNo = Long.valueOf(invoice.getOrderNo().getOrderNo());
      this.invoiceMainNo = invoice.getInvoiceMainNo();
      this.invoiceRedBlueTypeEnum = invoice.getInvoiceRedBlueType();
    }
  }

}
