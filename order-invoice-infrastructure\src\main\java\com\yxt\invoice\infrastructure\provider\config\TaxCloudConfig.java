package com.yxt.invoice.infrastructure.provider.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: moatkon
 * @time: 2024/12/27 9:40
 */
@Configuration
@ConfigurationProperties(prefix = "provider.tax-cloud")
@Data
public class TaxCloudConfig {
    private Map<String, Object> config = new HashMap<>();


}

