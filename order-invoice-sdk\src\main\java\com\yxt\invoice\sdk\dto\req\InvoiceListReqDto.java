package com.yxt.invoice.sdk.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * 发票列表查询请求
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Data
public class InvoiceListReqDto extends PageDateConditionDto implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 分公司编码
     */
    private String companyCode;

    /**
     * 机构编码
     */
    private String organizationCode;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * pos销售单号
     */
    private String posNo;

    /**
     * 开票单号
     */
    private String invoiceMainNo;

    /**
     * 发票状态
     */
    private String invoiceStatus;

    /**
     * 发票类型
     */
    private String invoiceType;

    /**
     * 购方类型
     */
    private String buyerPartyType;


}
