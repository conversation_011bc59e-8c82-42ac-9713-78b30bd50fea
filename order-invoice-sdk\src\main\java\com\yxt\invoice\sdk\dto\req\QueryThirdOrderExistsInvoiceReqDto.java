package com.yxt.invoice.sdk.dto.req;

import com.yxt.lang.dto.api.MiddleRequestBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class QueryThirdOrderExistsInvoiceReqDto extends MiddleRequestBase implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 第三方平台订单号
     */
    @ApiModelProperty("三方平台订单号")
    private String thirdOrderNo;


}
