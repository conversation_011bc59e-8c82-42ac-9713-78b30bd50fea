package com.yxt.invoice.infrastructure.message;

import com.yxt.common.ddd.domain.producer.BaseDomainEventProducer;
import com.yxt.invoice.domain.event.BaseInvoiceDomainEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月07日 10:04
 * @email: <EMAIL>
 */
@Slf4j
@Service
public class InvoiceDomainEventProducer extends BaseDomainEventProducer<BaseInvoiceDomainEvent<?>> {

//  @Resource
//  private RocketMQTemplate template;

    @Value("${mq.topic.producer.offlineOrderProducer:''}")
    private String offlineOrderProducer;

    @Override
    public void sendDomainEvent(BaseInvoiceDomainEvent<?> domainEvent) {

        //todo 消费事件代码
    }


    public void sendDomainEvents(List<BaseInvoiceDomainEvent<?>> domainEvents) {
        for (BaseInvoiceDomainEvent<?> domainEvent : domainEvents) {
            sendDomainEvent(domainEvent);
        }
    }


}
