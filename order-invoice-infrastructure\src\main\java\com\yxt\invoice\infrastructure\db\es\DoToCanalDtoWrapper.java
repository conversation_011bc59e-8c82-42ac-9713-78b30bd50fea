package com.yxt.invoice.infrastructure.db.es;

import com.yxt.invoice.infrastructure.db.es.es_invoice_main.handler.data.CanalInvoice.Invoice;
import com.yxt.invoice.infrastructure.db.mysql.entity.InvoiceMainDO;

/**
 * 从DO转Canal Data Dto
 * @author: moatkon
 * @time: 2024/12/24 11:13
 */
public class DoToCanalDtoWrapper {

  public static Invoice getInvoice(InvoiceMainDO data) {
    Invoice invoice =  new Invoice();
    invoice.setInvoiceMainNo(data.getInvoiceMainNo());
    invoice.setUserId(data.getUserId());
    invoice.setCompanyCode(data.getCompanyCode());
    invoice.setOrganizationCode(data.getOrganizationCode());
    invoice.setThirdPlatformCode(data.getThirdPlatformCode());
    invoice.setThirdOrderNo(data.getThirdOrderNo());
    invoice.setOrderNo(data.getOrderNo());
    invoice.setPosNo(data.getPosNo());
    invoice.setInvoiceRedBlueType(data.getInvoiceRedBlueType());
    invoice.setInvoiceStatus(data.getInvoiceStatus());
    invoice.setBuyerPartyType(data.getBuyerPartyType());
    invoice.setSyncStatus(data.getSyncStatus());
    invoice.setIsValid(data.getIsValid());
    invoice.setApplyTime(data.getApplyTime());
    return invoice;

  }
}
