package com.yxt.invoice.domain.event;

import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.lang.dto.OperateContext;
import com.yxt.lang.util.JsonUtils;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

/**
 * 发票模型事件
 *
 * <AUTHOR> (moatkon)
 * @date 2024年04月03日 18:29
 * @email: <EMAIL>
 */
@Slf4j
@NoArgsConstructor
public class BaseUpdateInvoiceDomainEvent<T> extends BaseInvoiceDomainEvent<T> {


  protected BaseUpdateInvoiceDomainEvent(InvoiceAggregate aggregate,
                                         OperateContext operateContext, String type, T data) {
    // 对于更新的业务主键是 String.valueOf(aggregate.getOrderInfo().getOrderNo())
    super(String.valueOf(aggregate.getInvoiceMain().getOrderNo()), operateContext,type, data);
    // event log
    log.info("{} >>> {},data:{}", SOURCE, type, JsonUtils.toJson(data));
  }

  @Getter
  @Setter
  @ToString(callSuper = true)
  public static class BaseData {

    private String merCode;
    private Long orderNo;

    protected void convert(InvoiceAggregate aggregate) {
      InvoiceMain invoice = aggregate.getInvoiceMain();
      this.merCode = invoice.getMerCode();
      this.orderNo = Long.valueOf(invoice.getOrderNo().getOrderNo());
    }
  }

}
