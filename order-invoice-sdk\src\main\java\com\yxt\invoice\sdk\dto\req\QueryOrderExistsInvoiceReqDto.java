package com.yxt.invoice.sdk.dto.req;

import com.yxt.lang.dto.api.MiddleRequestBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class QueryOrderExistsInvoiceReqDto extends MiddleRequestBase implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号,必填")
    private String orderNo;


    /**
     * 交易场景 online:线上交易, offline:线下交易
     */
    @ApiModelProperty("交易场景 online:线上交易, offline:线下交易,必填")
    private String transactionChannel;

    /**
     * 业务类型 O2O, B2C
     */
    @ApiModelProperty("业务类型 O2O、B2C, 线下单填O2O , 必填 ")
    private String businessType;

    /**
     * posNo
     */
    @ApiModelProperty("线下交易时必填")
    private String posNo;



}
