package com.yxt.invoice.interfaces.converter;

import com.yxt.invoice.domain.model.InvoiceDetail;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.model.valueobject.ExistsOrderInvoice;
import com.yxt.invoice.domain.model.valueobject.InvoiceAmount;
import com.yxt.invoice.sdk.dto.InvoiceAmountDTO;
import com.yxt.invoice.sdk.dto.InvoiceDetailDTO;
import com.yxt.invoice.sdk.dto.InvoiceMainDTO;
import com.yxt.invoice.sdk.dto.res.ApplyInvoiceResDto;
import com.yxt.invoice.sdk.dto.res.ApplyRedCreditResDto;
import com.yxt.invoice.sdk.dto.res.InvoiceDetailResponse;
import com.yxt.invoice.sdk.dto.res.QueryOrderExistsInvoiceResDto;
import com.yxt.order.types.offline.OfflineOrderNo;
import com.yxt.order.types.offline.OfflineThirdOrderNo;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 发票DTO转换器
 * 负责Domain对象与SDK DTO之间的转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public class InvoiceDTOConverter {


    /**
     * SDK DTO转换为Domain对象 - InvoiceMain
     */
    public static InvoiceMain convertToInvoiceMain(InvoiceMainDTO dto) {
        if (dto == null) {
            return null;
        }

        InvoiceMain invoiceMain = new InvoiceMain();

        // 基本字段转换
        invoiceMain.setId(dto.getId());
        invoiceMain.setCompanyCode(dto.getCompanyCode());
        invoiceMain.setCompanyName(dto.getCompanyName());
        invoiceMain.setOrganizationCode(dto.getOrganizationCode());
        invoiceMain.setOrganizationName(dto.getOrganizationName());
        invoiceMain.setInvoiceMainNo(dto.getInvoiceMainNo());
        invoiceMain.setThirdPlatformCode(dto.getThirdPlatformCode());
        invoiceMain.setThirdOrderNo(OfflineThirdOrderNo.thirdOrderNo(dto.getThirdOrderNo()));
        invoiceMain.setOrderNo(OfflineOrderNo.orderNo(dto.getOrderNo()));
        invoiceMain.setPosNo(dto.getPosNo());
        invoiceMain.setUserId(dto.getUserId());
        invoiceMain.setMerCode(dto.getMerCode());
        invoiceMain.setTransactionChannel(dto.getTransactionChannel());
        invoiceMain.setProviderCode(dto.getProviderCode());
        invoiceMain.setInvoiceCode(dto.getInvoiceCode());
        invoiceMain.setInvoiceNo(dto.getInvoiceNo());
        invoiceMain.setInvoiceRedBlueType(dto.getInvoiceRedBlueType());
        invoiceMain.setRedInvoiceMainNo(dto.getRedInvoiceMainNo());
        invoiceMain.setRedInvoiceReason(dto.getRedInvoiceReason());
        invoiceMain.setNotes(dto.getNotes());

        // 枚举类型直接赋值（SDK和Domain使用相同的枚举）
        invoiceMain.setInvoiceType(dto.getInvoiceType());
        invoiceMain.setInvoiceStatus(dto.getInvoiceStatus());
        invoiceMain.setSyncStatus(dto.getSyncStatus());
        invoiceMain.setBuyerPartyType(dto.getBuyerPartyType());

        // 金额字段
        invoiceMain.setActualPayAmount(dto.getActualPayAmount());
        invoiceMain.setDeliveryAmount(dto.getDeliveryAmount());
        invoiceMain.setInvoiceAmount(dto.getInvoiceAmount());
        invoiceMain.setTaxAmount(dto.getTaxAmount());
        invoiceMain.setPriceTaxAmount(dto.getPriceTaxAmount());

        // 其他字段
        invoiceMain.setDeliveryType(dto.getDeliveryType());
        invoiceMain.setSplitBill(dto.getSplitBill());
        invoiceMain.setOrderCreated(dto.getOrderCreated());
        invoiceMain.setPdfUrl(dto.getPdfUrl());
        invoiceMain.setInvoiceErrMsg(dto.getInvoiceErrMsg());
        invoiceMain.setApplyTime(dto.getApplyTime());
        invoiceMain.setOperator(dto.getOperator());
        invoiceMain.setPayee(dto.getPayee());
        invoiceMain.setReviewed(dto.getReviewed());
        invoiceMain.setApplyChannel(dto.getApplyChannel());

        // 销方信息
        invoiceMain.setSellerNumber(dto.getSellerNumber());
        invoiceMain.setSellerName(dto.getSellerName());
        invoiceMain.setSellerTin(dto.getSellerTin());
        invoiceMain.setSellerAddress(dto.getSellerAddress());
        invoiceMain.setSellerPhone(dto.getSellerPhone());
        invoiceMain.setSellerBank(dto.getSellerBank());
        invoiceMain.setSellerBankAccount(dto.getSellerBankAccount());

        // 购方信息
        invoiceMain.setBuyerName(dto.getBuyerName());
        invoiceMain.setBuyerTin(dto.getBuyerTin());
        invoiceMain.setBuyerAddress(dto.getBuyerAddress());
        invoiceMain.setBuyerPhone(dto.getBuyerPhone());
        invoiceMain.setBuyerBank(dto.getBuyerBank());
        invoiceMain.setBuyerBankAccount(dto.getBuyerBankAccount());
        invoiceMain.setBuyerEmail(dto.getBuyerEmail());
        invoiceMain.setBuyerMobile(dto.getBuyerMobile());
        invoiceMain.setShowBuyerBankAccount(dto.getShowBuyerBankAccount());

        // 系统字段
        invoiceMain.setIsValid(dto.getIsValid());
        invoiceMain.setCreated(dto.getCreated());
        invoiceMain.setUpdated(dto.getUpdated());
        invoiceMain.setCreatedBy(dto.getCreatedBy());
        invoiceMain.setUpdatedBy(dto.getUpdatedBy());
        invoiceMain.setSysCreateTime(dto.getSysCreateTime());
        invoiceMain.setSysUpdateTime(dto.getSysUpdateTime());
        invoiceMain.setVersion(dto.getVersion());

        return invoiceMain;
    }

    /**
     * Domain对象转换为SDK DTO - InvoiceMainDTO
     */
    public static InvoiceMainDTO convertToInvoiceMainDTO(InvoiceMain invoiceMain) {
        if (invoiceMain == null) {
            return null;
        }

        InvoiceMainDTO dto = new InvoiceMainDTO();

        // 基本字段转换
        dto.setId(invoiceMain.getId());
        dto.setCompanyCode(invoiceMain.getCompanyCode());
        dto.setCompanyName(invoiceMain.getCompanyName());
        dto.setOrganizationCode(invoiceMain.getOrganizationCode());
        dto.setOrganizationName(invoiceMain.getOrganizationName());
        dto.setInvoiceMainNo(invoiceMain.getInvoiceMainNo());
        dto.setThirdPlatformCode(invoiceMain.getThirdPlatformCode());
        dto.setThirdOrderNo(invoiceMain.getThirdOrderNo().getThirdOrderNo());
        dto.setOrderNo(invoiceMain.getOrderNo().getOrderNo());
        dto.setPosNo(invoiceMain.getPosNo());
        dto.setUserId(invoiceMain.getUserId());
        dto.setMerCode(invoiceMain.getMerCode());
        dto.setTransactionChannel(invoiceMain.getTransactionChannel());
        dto.setProviderCode(invoiceMain.getProviderCode());
        dto.setInvoiceCode(invoiceMain.getInvoiceCode());
        dto.setInvoiceNo(invoiceMain.getInvoiceNo());
        dto.setInvoiceRedBlueType(invoiceMain.getInvoiceRedBlueType());
        dto.setRedInvoiceMainNo(invoiceMain.getRedInvoiceMainNo());
        dto.setRedInvoiceReason(invoiceMain.getRedInvoiceReason());
        dto.setNotes(invoiceMain.getNotes());

        // 枚举类型直接赋值
        dto.setInvoiceType(invoiceMain.getInvoiceType());
        dto.setInvoiceStatus(invoiceMain.getInvoiceStatus());
        dto.setSyncStatus(invoiceMain.getSyncStatus());
        dto.setBuyerPartyType(invoiceMain.getBuyerPartyType());

        // 金额字段
        dto.setActualPayAmount(invoiceMain.getActualPayAmount());
        dto.setDeliveryAmount(invoiceMain.getDeliveryAmount());
        dto.setInvoiceAmount(invoiceMain.getInvoiceAmount());
        dto.setTaxAmount(invoiceMain.getTaxAmount());
        dto.setPriceTaxAmount(invoiceMain.getPriceTaxAmount());

        // 其他字段
        dto.setDeliveryType(invoiceMain.getDeliveryType());
        dto.setSplitBill(invoiceMain.getSplitBill());
        dto.setOrderCreated(invoiceMain.getOrderCreated());
        dto.setPdfUrl(invoiceMain.getPdfUrl());
        dto.setInvoiceErrMsg(invoiceMain.getInvoiceErrMsg());
        dto.setApplyTime(invoiceMain.getApplyTime());
        dto.setOperator(invoiceMain.getOperator());
        dto.setPayee(invoiceMain.getPayee());
        dto.setReviewed(invoiceMain.getReviewed());
        dto.setApplyChannel(invoiceMain.getApplyChannel());

        // 销方信息
        dto.setSellerNumber(invoiceMain.getSellerNumber());
        dto.setSellerName(invoiceMain.getSellerName());
        dto.setSellerTin(invoiceMain.getSellerTin());
        dto.setSellerAddress(invoiceMain.getSellerAddress());
        dto.setSellerPhone(invoiceMain.getSellerPhone());
        dto.setSellerBank(invoiceMain.getSellerBank());
        dto.setSellerBankAccount(invoiceMain.getSellerBankAccount());

        // 购方信息
        dto.setBuyerName(invoiceMain.getBuyerName());
        dto.setBuyerTin(invoiceMain.getBuyerTin());
        dto.setBuyerAddress(invoiceMain.getBuyerAddress());
        dto.setBuyerPhone(invoiceMain.getBuyerPhone());
        dto.setBuyerBank(invoiceMain.getBuyerBank());
        dto.setBuyerBankAccount(invoiceMain.getBuyerBankAccount());
        dto.setBuyerEmail(invoiceMain.getBuyerEmail());
        dto.setBuyerMobile(invoiceMain.getBuyerMobile());
        dto.setShowBuyerBankAccount(invoiceMain.getShowBuyerBankAccount());

        // 系统字段
        dto.setIsValid(invoiceMain.getIsValid());
        dto.setCreated(invoiceMain.getCreated());
        dto.setUpdated(invoiceMain.getUpdated());
        dto.setCreatedBy(invoiceMain.getCreatedBy());
        dto.setUpdatedBy(invoiceMain.getUpdatedBy());
        dto.setSysCreateTime(invoiceMain.getSysCreateTime());
        dto.setSysUpdateTime(invoiceMain.getSysUpdateTime());
        dto.setVersion(invoiceMain.getVersion());

        return dto;
    }

    /**
     * SDK DTO转换为Domain对象 - InvoiceDetail
     */
    public static InvoiceDetail convertToInvoiceDetail(InvoiceDetailDTO dto) {
        if (dto == null) {
            return null;
        }

        InvoiceDetail invoiceDetail = new InvoiceDetail();

        invoiceDetail.setId(dto.getId());
        invoiceDetail.setInvoiceMainNo(dto.getInvoiceMainNo());
        invoiceDetail.setInvoiceDetailNo(dto.getInvoiceDetailNo());
        invoiceDetail.setRowNo(dto.getRowNo());
        invoiceDetail.setTaxClassificationCode(dto.getTaxClassificationCode());
        invoiceDetail.setTopLevelTaxClassificationCode(dto.getTopLevelTaxClassificationCode());
        invoiceDetail.setErpCode(dto.getErpCode());
        invoiceDetail.setErpName(dto.getErpName());
        invoiceDetail.setCommodityCount(dto.getCommodityCount());
        invoiceDetail.setCommoditySpec(dto.getCommoditySpec());
        invoiceDetail.setUnit(dto.getUnit());
        invoiceDetail.setPrice(dto.getPrice());
        invoiceDetail.setTotalAmount(dto.getTotalAmount());
        invoiceDetail.setTaxAmount(dto.getTaxAmount());
        invoiceDetail.setTaxRate(dto.getTaxRate());
        invoiceDetail.setTaxRateCode(dto.getTaxRateCode());
        invoiceDetail.setPriceTaxAmount(dto.getPriceTaxAmount());
        invoiceDetail.setInvoiceLineType(dto.getInvoiceLineType());
        invoiceDetail.setDiscountAmount(dto.getDiscountAmount());
        invoiceDetail.setPolicyStatus(dto.getPolicyStatus());
        invoiceDetail.setPolicyTag(dto.getPolicyTag());
        invoiceDetail.setPolicyTaxRate(dto.getPolicyTaxRate());
        invoiceDetail.setIsValid(dto.getIsValid());
        invoiceDetail.setCreated(dto.getCreated());
        invoiceDetail.setUpdated(dto.getUpdated());
        invoiceDetail.setCreatedBy(dto.getCreatedBy());
        invoiceDetail.setUpdatedBy(dto.getUpdatedBy());
        invoiceDetail.setSysCreateTime(dto.getSysCreateTime());
        invoiceDetail.setSysUpdateTime(dto.getSysUpdateTime());
        invoiceDetail.setVersion(dto.getVersion());

        return invoiceDetail;
    }

    /**
     * Domain对象转换为SDK DTO - InvoiceDetailDTO
     */
    public static InvoiceDetailDTO convertToInvoiceDetailDTO(InvoiceDetail invoiceDetail) {
        if (invoiceDetail == null) {
            return null;
        }

        InvoiceDetailDTO dto = new InvoiceDetailDTO();

        dto.setId(invoiceDetail.getId());
        dto.setInvoiceMainNo(invoiceDetail.getInvoiceMainNo());
        dto.setInvoiceDetailNo(invoiceDetail.getInvoiceDetailNo());
        dto.setRowNo(invoiceDetail.getRowNo());
        dto.setTaxClassificationCode(invoiceDetail.getTaxClassificationCode());
        dto.setTopLevelTaxClassificationCode(invoiceDetail.getTopLevelTaxClassificationCode());
        dto.setErpCode(invoiceDetail.getErpCode());
        dto.setErpName(invoiceDetail.getErpName());
        dto.setCommodityCount(invoiceDetail.getCommodityCount());
        dto.setCommoditySpec(invoiceDetail.getCommoditySpec());
        dto.setUnit(invoiceDetail.getUnit());
        dto.setPrice(invoiceDetail.getPrice());
        dto.setTotalAmount(invoiceDetail.getTotalAmount());
        dto.setTaxAmount(invoiceDetail.getTaxAmount());
        dto.setTaxRate(invoiceDetail.getTaxRate());
        dto.setTaxRateCode(invoiceDetail.getTaxRateCode());
        dto.setPriceTaxAmount(invoiceDetail.getPriceTaxAmount());
        dto.setInvoiceLineType(invoiceDetail.getInvoiceLineType());
        dto.setDiscountAmount(invoiceDetail.getDiscountAmount());
        dto.setPolicyStatus(invoiceDetail.getPolicyStatus());
        dto.setPolicyTag(invoiceDetail.getPolicyTag());
        dto.setPolicyTaxRate(invoiceDetail.getPolicyTaxRate());
        dto.setIsValid(invoiceDetail.getIsValid());
        dto.setCreated(invoiceDetail.getCreated());
        dto.setUpdated(invoiceDetail.getUpdated());
        dto.setCreatedBy(invoiceDetail.getCreatedBy());
        dto.setUpdatedBy(invoiceDetail.getUpdatedBy());
        dto.setSysCreateTime(invoiceDetail.getSysCreateTime());
        dto.setSysUpdateTime(invoiceDetail.getSysUpdateTime());
        dto.setVersion(invoiceDetail.getVersion());

        return dto;
    }

    public static ApplyInvoiceResDto convertToApplyInvoiceResDto(InvoiceAggregate aggregate) {
        ApplyInvoiceResDto dto = new ApplyInvoiceResDto();

        dto.setInvoiceMain(InvoiceDTOConverter.convertToInvoiceMainDTO(aggregate.getInvoiceMain()));
        dto.setDetails(aggregate.getInvoiceDetailList().stream().map(InvoiceDTOConverter::convertToInvoiceDetailDTO).collect(Collectors.toList()));
        return dto;
    }

    public static List<InvoiceDetail> convertToInvoiceDetails(List<InvoiceDetailDTO> details) {

        return details.stream().map(InvoiceDTOConverter::convertToInvoiceDetail).collect(Collectors.toList());
    }

    public static ApplyRedCreditResDto convertToApplyRedCreditResDto(InvoiceAggregate aggregate) {

        return new ApplyRedCreditResDto(aggregate.getInvoiceMain().getOrderNo().getOrderNo(),
                aggregate.getInvoiceMain().getInvoiceMainNo(),
                aggregate.getInvoiceMain().getInvoiceStatus(),
                aggregate.getInvoiceMain().getInvoiceErrMsg()
        );

    }

    public static InvoiceDetailResponse convertToInvoiceDetailResponse(InvoiceAggregate aggregate) {

        return new InvoiceDetailResponse(
                InvoiceDTOConverter.convertToInvoiceMainDTO(aggregate.getInvoiceMain()),
                aggregate.getInvoiceDetailList().stream().map(InvoiceDTOConverter::convertToInvoiceDetailDTO).collect(Collectors.toList())
        );
    }

    public static QueryOrderExistsInvoiceResDto convertToExistsOrderInvoice(ExistsOrderInvoice info) {

        QueryOrderExistsInvoiceResDto resDto = new QueryOrderExistsInvoiceResDto();
        resDto.setThirdPlatformCode(info.getThirdPlatformCode());
        resDto.setThirdOrderNo(info.getThirdOrderNo());
        resDto.setOrderNo(info.getOrderNo());
        resDto.setPosNo(info.getPosNo());
        resDto.setTransactionChannel(info.getTransactionChannel());
        resDto.setBusinessType(info.getBusinessType());
        resDto.setInvoiceMain(InvoiceDTOConverter.convertToInvoiceMainDTO(info.getInvoiceMain()));
        resDto.setInvoiceAmounts(info.getInvoiceAmounts().stream().map(InvoiceDTOConverter::convertToInvoiceAmountDTO).collect(Collectors.toList()));
        return resDto;

    }

    private static InvoiceAmountDTO convertToInvoiceAmountDTO(InvoiceAmount invoiceAmount) {

        return new InvoiceAmountDTO(invoiceAmount.getKey(), invoiceAmount.getAmount());

    }
}
