package com.yxt.invoice.sdk.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class InvoiceDetailDTO {

    /**
     * 明细ID
     */
    private Long id;

    /**
     * 开票单号
     */
    private String invoiceMainNo;

    /**
     * 开票单明细号
     */
    private String invoiceDetailNo;

    /**
     * 行号
     */
    private String rowNo;

    /**
     * 税收分类编码
     */
    private String taxClassificationCode;

    /**
     * 税收分类编码父级分类名称
     */
    private String topLevelTaxClassificationCode;

    /**
     * 商品编码
     */
    private String erpCode;

    /**
     * 商品名称
     */
    private String erpName;

    /**
     * 商品数量
     */
    private BigDecimal commodityCount;

    /**
     * 商品规格
     */
    private String commoditySpec;

    /**
     * 单位
     */
    private String unit;

    /**
     * 商品售价
     */
    private BigDecimal price;

    /**
     * 商品总额=price*数量
     */
    private BigDecimal totalAmount;

    /**
     * 行税额行金额/(1+税率)*税率
     */
    private BigDecimal taxAmount;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 税率编码
     */
    private String taxRateCode;

    /**
     * 价税合计
     */
    private BigDecimal priceTaxAmount;

    /**
     * 行性质 Regular_Line-正常行 Discount_Line-折扣行 Discounted_Line-被折扣行
     */
    private String invoiceLineType;

    /**
     * 抵扣金额
     */
    private BigDecimal discountAmount;

    /**
     * 启动优惠政策 YES-启用 NO-不启用
     */
    private String policyStatus;

    /**
     * 优惠标识
     */
    private String policyTag;

    /**
     * 优惠税率
     */
    private BigDecimal policyTaxRate;

    /**
     * 是否起效 1-起效 -1-未起效
     */
    private Long isValid;

    /**
     * 平台创建时间
     */
    private LocalDateTime created;

    /**
     * 平台更新时间
     */
    private LocalDateTime updated;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 系统创建时间
     */
    private LocalDateTime sysCreateTime;

    /**
     * 系统更新时间
     */
    private LocalDateTime sysUpdateTime;

    /**
     * 数据版本，每次update+1
     */
    private Long version;


}
