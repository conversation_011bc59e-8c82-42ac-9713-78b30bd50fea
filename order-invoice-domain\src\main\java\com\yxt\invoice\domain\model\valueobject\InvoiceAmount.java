package com.yxt.invoice.domain.model.valueobject;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@AllArgsConstructor
@Data
public class InvoiceAmount {

    @ApiModelProperty("类型  用户实付金额:invoiceAmount  用户实付金额/运费:invoiceAmountWithPostFee  用户实付金额+运费+补贴:invoiceAmountWithPostFeeWithSubsidy   用户实付金额+补贴:invoiceAmountWithSubsidy")
    private String key;

    @ApiModelProperty("发票金额")
    private String amount;
}
