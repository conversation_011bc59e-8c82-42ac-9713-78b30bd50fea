package com.yxt.invoice.sdk.dto.res;

import com.yxt.invoice.sdk.dto.InvoiceDetailDTO;
import com.yxt.invoice.sdk.dto.InvoiceMainDTO;
import com.yxt.lang.dto.api.ResponseDTO;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 发票申请响应
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Data
public class ApplyInvoiceResDto extends ResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 发票信息
     */
    private InvoiceMainDTO invoiceMain;

    /**
     * 发票明细列表
     */
    private List<InvoiceDetailDTO> details;

}
