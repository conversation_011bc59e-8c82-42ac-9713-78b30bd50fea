package com.yxt.invoice.application.command;

import java.math.BigDecimal;
import java.util.List;

/**
 * 发票申请命令
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public class InvoiceApplicationCommand {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 发票类型
     */
    private String invoiceType;

    /**
     * 购方类型
     */
    private String buyerPartyType;

    /**
     * 购方名称
     */
    private String buyerName;

    /**
     * 购方税号
     */
    private String buyerTin;

    /**
     * 购方地址
     */
    private String buyerAddress;

    /**
     * 购方电话
     */
    private String buyerPhone;

    /**
     * 购方银行
     */
    private String buyerBank;

    /**
     * 购方银行账户
     */
    private String buyerBankAccount;

    /**
     * 购方邮箱
     */
    private String buyerEmail;

    /**
     * 购方手机号
     */
    private String buyerMobile;

    /**
     * 实付金额
     */
    private BigDecimal actualPayAmount;

    /**
     * 配送费
     */
    private BigDecimal deliveryAmount;

    /**
     * 配送方式
     */
    private String deliveryType;

    /**
     * 发票明细列表
     */
    private List<InvoiceDetailCommand> details;

    /**
     * 私有构造函数
     */
    private InvoiceApplicationCommand() {
    }

    /**
     * 建造者模式
     */
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private InvoiceApplicationCommand command = new InvoiceApplicationCommand();

        public Builder orderNo(String orderNo) {
            command.orderNo = orderNo;
            return this;
        }

        public Builder userId(String userId) {
            command.userId = userId;
            return this;
        }

        public Builder invoiceType(String invoiceType) {
            command.invoiceType = invoiceType;
            return this;
        }

        public Builder buyerPartyType(String buyerPartyType) {
            command.buyerPartyType = buyerPartyType;
            return this;
        }

        public Builder buyerName(String buyerName) {
            command.buyerName = buyerName;
            return this;
        }

        public Builder buyerTin(String buyerTin) {
            command.buyerTin = buyerTin;
            return this;
        }

        public Builder buyerAddress(String buyerAddress) {
            command.buyerAddress = buyerAddress;
            return this;
        }

        public Builder buyerPhone(String buyerPhone) {
            command.buyerPhone = buyerPhone;
            return this;
        }

        public Builder buyerBank(String buyerBank) {
            command.buyerBank = buyerBank;
            return this;
        }

        public Builder buyerBankAccount(String buyerBankAccount) {
            command.buyerBankAccount = buyerBankAccount;
            return this;
        }

        public Builder buyerEmail(String buyerEmail) {
            command.buyerEmail = buyerEmail;
            return this;
        }

        public Builder buyerMobile(String buyerMobile) {
            command.buyerMobile = buyerMobile;
            return this;
        }

        public Builder actualPayAmount(BigDecimal actualPayAmount) {
            command.actualPayAmount = actualPayAmount;
            return this;
        }

        public Builder deliveryAmount(BigDecimal deliveryAmount) {
            command.deliveryAmount = deliveryAmount;
            return this;
        }

        public Builder deliveryType(String deliveryType) {
            command.deliveryType = deliveryType;
            return this;
        }

        public Builder details(List<InvoiceDetailCommand> details) {
            command.details = details;
            return this;
        }

        public InvoiceApplicationCommand build() {
            return command;
        }
    }

    // Getters
    public String getOrderNo() {
        return orderNo;
    }

    public String getUserId() {
        return userId;
    }

    public String getInvoiceType() {
        return invoiceType;
    }

    public String getBuyerPartyType() {
        return buyerPartyType;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public String getBuyerTin() {
        return buyerTin;
    }

    public String getBuyerAddress() {
        return buyerAddress;
    }

    public String getBuyerPhone() {
        return buyerPhone;
    }

    public String getBuyerBank() {
        return buyerBank;
    }

    public String getBuyerBankAccount() {
        return buyerBankAccount;
    }

    public String getBuyerEmail() {
        return buyerEmail;
    }

    public String getBuyerMobile() {
        return buyerMobile;
    }

    public BigDecimal getActualPayAmount() {
        return actualPayAmount;
    }

    public BigDecimal getDeliveryAmount() {
        return deliveryAmount;
    }

    public String getDeliveryType() {
        return deliveryType;
    }

    public List<InvoiceDetailCommand> getDetails() {
        return details;
    }
}
