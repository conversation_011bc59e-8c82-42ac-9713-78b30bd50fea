package com.yxt.invoice.infrastructure.db.es.es_invoice_main.doc;

import java.time.LocalDateTime;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

/**
 * @author: moatkon
 * @time: 2025/3/27 18:26
 */
@Data
@IndexName(value = "es_invoice_main", keepGlobalPrefix = true, aliasName = "alias_es_invoice_main")
public class EsInvoiceMain {

  @IndexId(type = IdType.CUSTOMIZE)
  private String invoiceMainNo;

  /**
   * 会员编号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String userId;

  /**
   * 分公司编码
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String companyCode;


  /**
   * 机构编码
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String organizationCode;


  /**
   * 第三方平台编码
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String thirdPlatformCode;


  /**
   * 第三方订单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String thirdOrderNo;

  /**
   * 订单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String orderNo;

  /**
   * pos销售单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String posNo;

  /**
   * 红蓝字标识
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String invoiceRedBlueType;

  /**
   * 发票状态
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String invoiceStatus;

  /**
   * 购方类型
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String buyerPartyType;


  /**
   * 同步状态
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String syncStatus;

  /**
   * 是否有效
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private Long isValid;

  /**
   * 申请时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime applyTime;

}
