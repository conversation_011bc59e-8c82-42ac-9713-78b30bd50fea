package com.yxt.invoice.infrastructure.db.es.es_invoice_main.flash;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.common.logic.flash.AbstractFlashData;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.invoice.infrastructure.db.es.DoToCanalDtoWrapper;
import com.yxt.invoice.infrastructure.db.es.InvoiceScene;
import com.yxt.invoice.infrastructure.db.es.es_invoice_main.handler.InvoiceMainHandler;
import com.yxt.invoice.infrastructure.db.es.es_invoice_main.handler.data.CanalInvoice;
import com.yxt.invoice.infrastructure.db.es.es_invoice_main.handler.data.CanalInvoice.Invoice;
import com.yxt.invoice.infrastructure.db.mysql.entity.InvoiceMainDO;

import com.yxt.invoice.infrastructure.db.mysql.mapper.InvoiceMainMapper;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2025/4/1 10:55
 */
@Component

public class EsInvoiceMainFlash extends
    AbstractFlashData<InvoiceMainDO, Invoice, InvoiceScene> {

  @Value("${invoiceFlashLimit:2000}")
  private Integer invoiceFlashLimit;

  @Resource
  private InvoiceMainHandler invoiceMainHandler;

  @Resource
  private InvoiceMainMapper invoiceMainMapper;



  @Override
  protected Long queryCursorStartId() {
    return invoiceMainMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return invoiceMainMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<InvoiceMainDO> getSourceList() {

    FlashParam flashParam = getFlashParam();

    LambdaQueryWrapper<InvoiceMainDO> query = new LambdaQueryWrapper<>();
    if (!CollectionUtils.isEmpty(flashParam.getNoList())) {
      query.in(InvoiceMainDO::getOrderNo, flashParam.getNoList());
    } else {
      query.ge(InvoiceMainDO::getId, flashParam.getCursorStartId());
      query.le(InvoiceMainDO::getId, flashParam.getCursorStartId() + invoiceFlashLimit);
    }

    return invoiceMainMapper.selectList(query);
  }

  @Override
  protected List<Invoice> assembleTargetData(List<InvoiceMainDO> sourceList) {
    return sourceList.stream().map(DoToCanalDtoWrapper::getInvoice).collect(Collectors.toList());
  }

  @Override
  protected void flash(List<Invoice> invoiceList) {
    CanalInvoice canalOfflineOrder = new CanalInvoice();
    canalOfflineOrder.setData(invoiceList);
    invoiceMainHandler.manualFlash(canalOfflineOrder);
  }



  @Override
  protected Integer defaultLimit() {
    return invoiceFlashLimit;
  }
}
