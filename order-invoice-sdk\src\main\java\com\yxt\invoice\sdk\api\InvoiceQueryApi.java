package com.yxt.invoice.sdk.api;

import com.yxt.invoice.sdk.dto.InvoiceMainDTO;
import com.yxt.invoice.sdk.dto.req.*;
import com.yxt.invoice.sdk.dto.res.InvoiceDetailResponse;
import com.yxt.invoice.sdk.dto.res.QueryOrderExistsInvoiceResDto;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;

/**
 * 发票服务SDK接口
 * 为医药电商平台提供开票相关服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public interface InvoiceQueryApi {


    /**
     * 查询发票列表
     *
     * @param request 发票列表查询请求
     * @return 发票列表结果
     */
    ResponseBase<PageDTO<InvoiceMainDTO>> queryInvoiceList(InvoiceListReqDto request);

    /**
     * 查询发票详情
     *
     * @param req 发票ID
     * @return 发票详情结果
     */
    ResponseBase<InvoiceDetailResponse> queryInvoiceDetail(InvoiceDetailReqDto req);



    /**
     * 查询订单是否开具发票
     *
     * @param req 发票ID
     * @return 发票详情结果
     */
    ResponseBase<QueryOrderExistsInvoiceResDto> queryOrderExistsInvoice(QueryOrderExistsInvoiceReqDto req);



    /**
     * 查询订单是否开具发票
     *
     * @param req 发票ID
     * @return 发票详情结果
     */
    ResponseBase<QueryOrderExistsInvoiceResDto> queryThirdOrderExistsInvoiceReqDto(QueryThirdOrderExistsInvoiceReqDto req);

}
