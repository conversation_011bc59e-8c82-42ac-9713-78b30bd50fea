package com.yxt.invoice.domain.command;

import com.yxt.lang.constants.ApiBizCodeEnum;
import com.yxt.lang.dto.api.MiddleRequestBase;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 发票红冲命令
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@NoArgsConstructor
@Data
public class RedCreditInvoiceCommand extends MiddleRequestBase implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开票单号
     */
    @NotBlank(message = "开票单号不能为空")
    private String invoiceMainNo;

    /**
     * 红冲原因
     * 01: 开票有误
     * 02: 销货退回
     * 03: 服务中止
     * 04: 销售折让
     */
    private String redCreditReason;
    /**
     * 备注
     */
    private String notes;
    /**
     * 操作人用户ID
     */
    private String operatorUserId;

    /**
     * 操作时间戳
     */
    private Date applyTime;

    public boolean isAllowRedInvoice(){
        return ApiBizCodeEnum.MERCHANT_PLATFORM.getCode().equals(this.getBizCode());
    }





}
