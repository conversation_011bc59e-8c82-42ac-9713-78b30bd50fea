package com.yxt.invoice.infrastructure.provider.dto.res;

import lombok.Data;

// 响应DTO
@Data
public class PostNegativeInvoiceIssueResDto {

        /**
         * 红冲业务流水号
         * 通过该业务单查询红字发票信息
         */
        private String responseId;
        /**
         * 红字录入单 UUID
         */
        private String uuid;
        /**
         * 红字确认单编号
         */
        private String confirmCode;
        /**
         * 红字确认代码
         * 01：无需确认
         * 02：销方录入待购方确认
         * 03：购方录入待销方确认
         * 04：购销双方已确认
         * 05：作废（销方录入购方否认）
         * 06：作废（购方录入销方否认）
         * 07：作废（超72 小时未确认）
         * 08：（发起方撤销）
         * 09：作废（确认后撤销）
         * 10：作废（异常凭证）
         */
        private String confirmStatus;


}
