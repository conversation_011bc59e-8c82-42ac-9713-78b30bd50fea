<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.yxt.ddd</groupId>
    <artifactId>order-invoice-service</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

  <groupId>com.yxt.ddd.service.starter</groupId>
  <artifactId>order-invoice-bootstrap</artifactId>
  <packaging>jar</packaging>
  <version>1.0.0</version>

  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.yxt.ddd.interfaces</groupId>
      <artifactId>order-invoice-interface</artifactId>
      <version>1.0.0</version>
    </dependency>

    <dependency>
      <groupId>com.yxt.ddd.infrastructure</groupId>
      <artifactId>order-invoice-infrastructure</artifactId>
      <version>1.0.0</version>
    </dependency>


  </dependencies>

  <build>
    <finalName>ddd-service</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>