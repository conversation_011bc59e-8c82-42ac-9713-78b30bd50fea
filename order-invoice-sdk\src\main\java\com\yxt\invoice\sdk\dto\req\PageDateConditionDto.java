package com.yxt.invoice.sdk.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.lang.dto.api.MiddleRequestBase;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月23日 11:25
 * @email: <EMAIL>
 */
@Data
public class PageDateConditionDto extends MiddleRequestBase  {

  /**
   * 查询条件 格式2024-04-22 00:00:00
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date startDate;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date endDate;

  /**
   * 当前页码
   */
  private Long currentPage = 1L;
  /**
   * 每页数量
   */
  private Long pageSize = 20L;

}
