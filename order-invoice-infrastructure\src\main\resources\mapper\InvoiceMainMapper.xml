<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.invoice.infrastructure.db.mysql.mapper.InvoiceMainMapper">


  <select id="selectMaxId" resultType="java.lang.Long">
    select max(id) from invoice_main
    <include refid="flashParamSql" />
  </select>

  <select id="selectMinId" resultType="java.lang.Long">
    select min(id) from invoice_main
    <include refid="flashParamSql" />
  </select>

  <sql id="flashParamSql">
    where 1=1
    <if test="flashParam.start != null">
      and apply_time <![CDATA[ >= ]]> #{flashParam.start}
    </if>
    <if test="flashParam.end != null">
      and apply_time <![CDATA[ <= ]]> #{flashParam.end}
    </if>
    <if test="flashParam.noList != null and flashParam.noList.size() > 0">
      and invoice_main_no in
      <foreach collection="flashParam.noList" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
  </sql>

</mapper>