package com.yxt.invoice.infrastructure.provider.dto.res;

import lombok.Data;

import java.math.BigDecimal;

// 响应DTO
@Data
public class GetInvoiceByResponseIdResDto {

        /**
         * 发票号码
         */
        private String invoiceCode;
        /**
         * 发票标识
         * 0 蓝票，1 红票
         */
        private String invoiceTag;
        /**
         * pdf 发票
         */
        private String downPdf;
        /**
         * 发票地址
         * 下载地址（含.xml、.odf、.pdf 格式）
         */
        private String invoicePdf;
        /**
         * 业务流水号
         */
        private String responseId;
        /**
         * 开票通道
         */
        private String channel;
        /**
         * 平台 ID
         */
        private String pId;
        /**
         * 开票日期
         */
        private String issueDate;
        /**
         * 发票上传状态
         * 00：上传成功
         * 01：发票上传处理中
         * 02：上传失败
         * 03：重复上传
         * 99：待上传
         */
        private String uploadStatus;
        /**
         * 发票状态
         * 01：蓝票申请
         * 02：红冲申请
         * 03：蓝票申请失败
         * 04：红票发票失败
         * 05：号码已分配
         * 06：蓝票开具成功
         * 07：红票开具成功
         * 08：红字确认单录入中
         */
        private String invoiceStatus;
        /**
         * 价税合计
         * 仅蓝票返回
         */
        private BigDecimal priceTaxAmount;
        /**
         * 发票金额
         * 仅蓝票返回
         */
        private BigDecimal invoiceAmount;
        /**
         * 税额
         * 仅蓝票返回
         */
        private BigDecimal taxAmount;
        /**
         * 红冲状态：
         * 0 待处理，
         * 1 处理中，
         * 2 处理完成,
         * 3 错误
         * 仅红字发票返回
         *
         */
        private String writeOffState;
        /**
         * 红字冲销金额
         */
        private BigDecimal hzcxje;
        /**
         * 红字冲销税额
         */
        private BigDecimal hzcxse;
        /**
         * 红字确认单状态代码
         * 00:未确认
         * 01:确认中
         * 02:确认失败
         * 03:确认完成
         */
        private String hzqrxxztDm;
        /**
         * 红字发票 uuid
         */
        private String uuid;
        /**
         * 红字确认单编号
         */
        private String hzfpxxqrdbh;
        /**
         * 冲红原因:
         * 01：开票有误
         * 02：销货退回
         * 03：服务终止
         * 04：销售折让
         */
        private String chyyDm;
        /**
         * 状态描述
         */
        private String statusMsg;

}
