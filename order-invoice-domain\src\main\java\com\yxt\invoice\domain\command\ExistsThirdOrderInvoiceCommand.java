package com.yxt.invoice.domain.command;

import com.yxt.lang.dto.api.MiddleRequestBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发票详情查询
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Data
@NoArgsConstructor
public class ExistsThirdOrderInvoiceCommand extends MiddleRequestBase {




    /**
     * 第三方平台订单号
     */
    @ApiModelProperty("三方平台订单号")
    private String thirdOrderNo;

}
