package com.yxt.invoice.domain.repository;

import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.model.valueobject.ProviderInvoiceInfo;

/**
 * 发票仓储接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public interface ProviderInvoiceRepository {


    /**
     * 申请供应商发票
     *
     * @param aggregate 发票聚合根
     * @return 保存后的发票聚合根
     */
    boolean applyProviderInvoice(InvoiceAggregate aggregate);


    /**
     * 查询供应商发票详情
     *
     * @param aggregate 发票聚合根
     * @return 供应商发票详情
     */
    ProviderInvoiceInfo getProviderInvoiceInfo(InvoiceAggregate aggregate);


    /**
     * 申请供应商红冲
     *
     * @param invoiceAggregate 发票聚合根
     * @param redInvoiceAggregate 发票聚合根
     * @return 保存后的发票聚合根
     */
    boolean applyProviderRedInvoice(InvoiceAggregate invoiceAggregate,InvoiceAggregate redInvoiceAggregate);


}
