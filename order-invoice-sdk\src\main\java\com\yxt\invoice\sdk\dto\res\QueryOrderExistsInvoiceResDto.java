package com.yxt.invoice.sdk.dto.res;

import com.yxt.invoice.sdk.dto.InvoiceAmountDTO;
import com.yxt.invoice.sdk.dto.InvoiceMainDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class QueryOrderExistsInvoiceResDto implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 平台编码
     */
    @ApiModelProperty("平台编码")
    private String thirdPlatformCode;

    /**
     * 第三方平台订单号
     */
    @ApiModelProperty("三方平台订单号")
    private String thirdOrderNo;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    private String orderNo;

    /**
     * pos销售单号
     */
    @ApiModelProperty("pos销售单号")
    private String posNo;

    /**
     * 交易场景 online:线上交易, offline:线下交易
     */
    @ApiModelProperty("交易场景 online:线上交易, offline:线下交易")
    private String transactionChannel;

    /**
     * 业务类型 O2O, B2C
     */
    @ApiModelProperty("业务类型 O2O、B2C ,  线下单填O2O")
    private String businessType;

    /**
     * 是否已开票
     */
    @ApiModelProperty("发票信息 已申请时有值")
    private InvoiceMainDTO invoiceMain;

    @ApiModelProperty("未开票时有值,发票开具金额选项")
    private List<InvoiceAmountDTO> invoiceAmounts;


}
