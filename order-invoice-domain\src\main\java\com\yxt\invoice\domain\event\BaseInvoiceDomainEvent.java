package com.yxt.invoice.domain.event;

import com.yxt.common.ddd.domain.event.BaseDomainEvent;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.lang.dto.OperateContext;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.types.invoice.enums.InvoiceRedBlueTypeEnum;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

/**
 * 发票模型事件
 *
 * <AUTHOR> (moatkon)
 * @date 2024年04月03日 18:29
 * @email: <EMAIL>
 */
@Slf4j
@NoArgsConstructor
public class BaseInvoiceDomainEvent<T> extends BaseDomainEvent<T> {

  public static final String SOURCE = "BaseInvoiceDomainEvent";

  protected BaseInvoiceDomainEvent(String bizNo,
                                   OperateContext operateContext, String type, T data) {
    super(bizNo, operateContext, System.currentTimeMillis(), SOURCE, type, data);
  }



}
