package com.yxt.invoice.application.command;

/**
 * 发票红冲命令
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public class InvoiceRedCreditCommand {

    /**
     * 发票ID
     */
    private Long invoiceId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 红冲原因
     */
    private String redCreditReason;

    /**
     * 私有构造函数
     */
    private InvoiceRedCreditCommand() {
    }

    /**
     * 建造者模式
     */
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private InvoiceRedCreditCommand command = new InvoiceRedCreditCommand();

        public Builder invoiceId(Long invoiceId) {
            command.invoiceId = invoiceId;
            return this;
        }

        public Builder userId(String userId) {
            command.userId = userId;
            return this;
        }

        public Builder redCreditReason(String redCreditReason) {
            command.redCreditReason = redCreditReason;
            return this;
        }

        public InvoiceRedCreditCommand build() {
            return command;
        }
    }

    // Getters
    public Long getInvoiceId() {
        return invoiceId;
    }

    public String getUserId() {
        return userId;
    }

    public String getRedCreditReason() {
        return redCreditReason;
    }
}
