package com.yxt.invoice.domain.model.valueobject;

import com.yxt.order.types.invoice.enums.InvoiceRedBlueTypeEnum;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProviderInvoiceInfo {

     /**
      * 业务流水号
      */
     private String responseId;
     /**
      * 发票单号
      */
     private String invoiceMainNo;
//     /**
//      * 发票代码（税务云回调写入）
//      */
//     private String invoiceCode;

     /**
      * 发票号码（税务云回调写入）
      */
     private String invoiceNo;

     /**
      *
      */
     private String pdfUrl;

     /**
      * 蓝票:Tax_Invoice 红票:Credit_Note
      */
     private InvoiceRedBlueTypeEnum invoiceRedBlueType;

     /**
      * 开票通道
      */
     private String channel;
     /**
      * 平台 ID
      */
     private String pId;
     /**
      * 开票日期
      */
     private String applyTime;
     /**
      * 发票上传状态
      * 00：上传成功
      * 01：发票上传处理中
      * 02：上传失败
      * 03：重复上传
      * 99：待上传
      */
     private String uploadStatus;

     /**
      * 发票状态
      */
     private String invoiceStatus;


     /**
      * 价税合计
      * 仅蓝票返回
      */
     private BigDecimal priceTaxAmount;
     /**
      * 发票金额
      * 仅蓝票返回
      */
     private BigDecimal invoiceAmount;
     /**
      * 税额
      * 仅蓝票返回
      */
     private BigDecimal taxAmount;
     /**
      * 红冲状态：
      * 仅红字发票返回
      *
      */
     private String redInvoiceStatus;
     /**
      * 红字冲销金额
      */
     private BigDecimal redInvoiceAmount;
     /**
      * 红字冲销税额
      */
     private BigDecimal redTaxAmount;
     /**
      * 红字确认单状态代码
      * 00:未确认
      * 01:确认中
      * 02:确认失败
      * 03:确认完成
      */
     private String confirmStatus;
     /**
      * 红字发票 uuid
      */
     private String uuid;
     /**
      * 红字确认单编号
      */
     private String confirmCode;
     /**
      * 冲红原因:
      * 01：开票有误
      * 02：销货退回
      * 03：服务终止
      * 04：销售折让
      */
     private String redInvoiceReason;
     /**
      * 状态描述
      */
     private String statusMsg;


}
