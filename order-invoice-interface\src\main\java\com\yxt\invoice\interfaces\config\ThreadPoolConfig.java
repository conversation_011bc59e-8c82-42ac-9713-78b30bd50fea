package com.yxt.invoice.interfaces.config;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/18 11:03
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "threadpool")
public class ThreadPoolConfig {

  // 延迟线程池
  public final static String SCHEDULED_THREAD_POOL = "scheduledThreadPool";
  public final static String COMMON_BUSINESS_POOL = "commonBusinessPool";

  private Integer commonBusinessPoolConsumerCoreSize = 8;
  private Integer commonBusinessPoolConsumerMaxSize = 48;
  private Long commonBusinessPoolConsumerKeepAliveTime = 3000L;
  private Integer commonBusinessPoolConsumerCapacity = 128;
  private Integer queryCapacity = 128;

  @Bean(SCHEDULED_THREAD_POOL)
  public ScheduledExecutorService scheduledThreadPool() {
    return new ScheduledThreadPoolExecutor(
        10,
        new ThreadFactoryBuilder().setNamePrefix(SCHEDULED_THREAD_POOL).build(),
        new ThreadPoolExecutor.CallerRunsPolicy()
    );
  }


  @Bean(COMMON_BUSINESS_POOL)
  public Executor commonBusinessPool() {
    return new ThreadPoolExecutor(
        commonBusinessPoolConsumerCoreSize,
        commonBusinessPoolConsumerMaxSize,
        commonBusinessPoolConsumerKeepAliveTime,
        TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<>(queryCapacity),
        new ThreadFactoryBuilder().setNamePrefix(COMMON_BUSINESS_POOL).build(),
        new ThreadPoolExecutor.CallerRunsPolicy()
    );
  }

}
