package com.yxt.invoice.infrastructure.provider.dto.res;

import lombok.Data;

// 响应DTO
@Data
public class PositiveInvoiceIssueResDto {

    /**
     * 每项处理状态 0 成功，1 失败
     */
    private Integer code;
    /**
     * 状态描述
     */
    private String msg;
    /**
     * 请求号
     */
    private String outRequestCode;
    /**
     * 发票标识 0，蓝票，1 红票
     */
    private String invoiceTag;
    /**
     * 正数发票号码
     */
    private String  invoiceCode;
    /**
     * 蓝票受理业务流水号
     */
    private String responseId;


    public boolean isSuccess() {
        return code == 0;
    }

}