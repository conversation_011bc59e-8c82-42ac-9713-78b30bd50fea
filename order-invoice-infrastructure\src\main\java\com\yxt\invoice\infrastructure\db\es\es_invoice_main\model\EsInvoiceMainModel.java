package com.yxt.invoice.infrastructure.db.es.es_invoice_main.model;

import com.yxt.common.logic.es.BaseEsIndexModel;
import com.yxt.invoice.infrastructure.db.es.es_invoice_main.doc.EsInvoiceMain;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class EsInvoiceMainModel extends BaseEsIndexModel {

  private String invoiceMainNo;

  /**
   * 会员编号
   */
  private String userId;

  /**
   * 分公司编码
   */
  private String companyCode;


  /**
   * 机构编码
   */
  private String organizationCode;


  /**
   * 第三方平台编码
   */
  private String thirdPlatformCode;


  /**
   * 第三方订单号
   */
  private String thirdOrderNo;

  /**
   * 订单号
   */
  private String orderNo;

  /**
   * pos销售单号
   */
  private String posNo;

  /**
   * 红蓝字标识
   */
  private String invoiceRedBlueType;

  /**
   * 发票状态
   */
  private String invoiceStatus;

  /**
   * 购方类型
   */
  private String buyerPartyType;


  /**
   * 同步状态
   */
  private String syncStatus;

  /**
   * 是否有效
   */
  private Long isValid;

  /**
   * 申请时间
   */
  private LocalDateTime applyTime;


  public EsInvoiceMain create() {
    EsInvoiceMain esInvoiceMain = new EsInvoiceMain();
    esInvoiceMain.setInvoiceMainNo(this.getInvoiceMainNo());
    esInvoiceMain.setUserId(this.getUserId());
    esInvoiceMain.setCompanyCode(this.getCompanyCode());
    esInvoiceMain.setOrganizationCode(this.getOrganizationCode());
    esInvoiceMain.setThirdPlatformCode(this.getThirdPlatformCode());
    esInvoiceMain.setThirdOrderNo(this.getThirdOrderNo());
    esInvoiceMain.setOrderNo(this.getOrderNo());
    esInvoiceMain.setPosNo(this.getPosNo());
    esInvoiceMain.setInvoiceRedBlueType(this.getInvoiceRedBlueType());
    esInvoiceMain.setInvoiceStatus(this.getInvoiceStatus());
    esInvoiceMain.setBuyerPartyType(this.getBuyerPartyType());
    esInvoiceMain.setSyncStatus(this.getSyncStatus());
    esInvoiceMain.setIsValid(this.getIsValid());
    esInvoiceMain.setApplyTime(this.getApplyTime());
    return esInvoiceMain;
  }

  public String defineId() {
    return String.valueOf(invoiceMainNo);
  }
}
