package com.yxt.invoice.application.service.impl;

import com.yxt.invoice.application.service.InvoiceApplicationService;
import com.yxt.invoice.domain.command.*;
import com.yxt.invoice.domain.factory.InvoiceAggregateFactory;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.model.valueobject.ExistsOrderInvoice;
import com.yxt.invoice.domain.repository.InvoiceRepository;
import com.yxt.invoice.domain.repository.ProviderInvoiceRepository;
import com.yxt.invoice.infrastructure.message.InvoiceDomainEventProducer;
import com.yxt.lang.dto.api.PageDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;


import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 发票应用服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Slf4j
@Service
public class InvoiceApplicationServiceImpl implements InvoiceApplicationService {


    @Resource
    private InvoiceRepository invoiceRepository;

    @Resource
    private ProviderInvoiceRepository providerInvoiceRepository;

    @Resource
    private InvoiceDomainEventProducer invoiceDomainEventProducer;

    @Resource
    private InvoiceAggregateFactory invoiceAggregateFactory;


    @Override
    public InvoiceAggregate simpleApplyInvoice(ApplyInvoiceCommand command) {
        return null;
    }

    @Override
    public InvoiceAggregate simpleRedCreditInvoice(RedCreditInvoiceCommand command) {
        log.info("开始红冲发票，开票单号：{}, 操作人ID：{}", command.getInvoiceMainNo(), command.getOperatorUserId());

        try {

            // 1. 查询发票聚合根
            InvoiceAggregate aggregate = invoiceRepository.findByInvoiceMainNo(command.getInvoiceMainNo());
            if (aggregate == null) {
                throw new IllegalArgumentException("发票不存在");
            }
            if (!aggregate.isAllowRedInvoice()) {
                throw new IllegalArgumentException("开具中,不允许红冲");
            }
            if (command.isAllowRedInvoice()) {
                throw new IllegalArgumentException("非店员发起,不允许红冲");
            }
            InvoiceAggregate redCreditInvoiceAggregate = invoiceAggregateFactory.redCreditAggregate(aggregate, command);


            // 4. 保存更新
            redCreditInvoiceAggregate = invoiceRepository.doSaveRedInvoice(aggregate, redCreditInvoiceAggregate);
            //  发送领域事件
            invoiceDomainEventProducer.sendDomainEvents(redCreditInvoiceAggregate.getDomainEvents());

            log.info("红冲发票成功，开票单号：{}", redCreditInvoiceAggregate.getInvoiceMain().getInvoiceMainNo());

            // 6. 返回聚合根
            return redCreditInvoiceAggregate;

        } catch (Exception e) {
            log.error("红冲发票失败，开票单号：{}, 错误：{}", command.getInvoiceMainNo(), e.getMessage(), e);
            throw e; // 重新抛出异常，由控制器处理
        }
    }


    @Override
    public ExistsOrderInvoice queryOrderExistsInvoice(ExistsOrderInvoiceCommand command) {
        return null;
    }

    @Override
    public ExistsOrderInvoice queryThirdOrderExistsInvoiceReqDto(ExistsThirdOrderInvoiceCommand command) {
        return null;
    }

    @Override
    @Transactional(readOnly = true)
    public PageDTO<InvoiceMain> queryInvoiceList(QueryInvoiceListCommand query) {
        try {
            return invoiceRepository.findInvoiceManByConditions(query);
        } catch (Exception e) {
            log.error("查询发票列表失败，错误：{}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public InvoiceAggregate queryInvoiceDetail(QueryInvoiceDetailCommand query) {
        log.info("查询发票详情，开票单号：{}, 订单号：{}", query.getInvoiceMainNo(), query.getOrderNo());
        try {
            // 查询发票聚合根
            InvoiceAggregate aggregate = null;
            if (StringUtils.isNotBlank(query.getInvoiceMainNo())) {
                aggregate = invoiceRepository.findByInvoiceMainNo(query.getInvoiceMainNo());
            } else if (StringUtils.isNotBlank(query.getOrderNo())) {
                aggregate = invoiceRepository.findByOrderNo(query.getOrderNo());
            }

            if (aggregate == null) {
                throw new IllegalArgumentException("发票不存在");
            }
            return aggregate;
        } catch (Exception e) {
            log.error("查询发票详情失败，错误：{}", e.getMessage(), e);
            throw e;
        }
    }


    @Override
    public InvoiceAggregate applyInvoice(ApplyInvoiceCommand command) {
        log.info("开始申请开票，平台订单号：{}, 订单号：{}, 用户ID：{}", command.getInvoiceMain().getThirdOrderNo().getThirdOrderNo(), command.getInvoiceMain().getOrderNo().getOrderNo(), command.getInvoiceMain().getUserId());
        try {
            // 命令转模型
            InvoiceAggregate aggravate = invoiceAggregateFactory.createAggravate(command);
            ExistsOrderInvoiceCommand existsOrderInvoiceCommand =aggravate.createExistsOrderInvoiceCommand();
            // 校验是否已申请
            if (invoiceRepository.exists(existsOrderInvoiceCommand)) {
                aggravate.existsApply();
                invoiceDomainEventProducer.sendDomainEvents(aggravate.getDomainEvents());
                return aggravate;
            }
            // 入库
            invoiceRepository.doSave(aggravate);
            //调用三方申请
            boolean b = providerInvoiceRepository.applyProviderInvoice(aggravate);

            //  发送领域事件
            invoiceDomainEventProducer.sendDomainEvents(aggravate.getDomainEvents());

            log.info("申请开票成功，发票ID：{}, 开票单号：{}",
                    aggravate.getInvoiceMain().getId(),
                    aggravate.getInvoiceMain().getInvoiceMainNo());
            return aggravate;
        } catch (Exception e) {
            log.error("申请开票失败，订单号：{}, 错误：{}", command.getInvoiceMain().getOrderNo(), e.getMessage(), e);
            throw e;
        }
    }


}
