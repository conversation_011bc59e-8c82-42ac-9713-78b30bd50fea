<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.yxt</groupId>
    <artifactId>yxt-xframe</artifactId>
    <version>2.16.5</version>
    <relativePath/>
  </parent>

  <groupId>com.yxt</groupId>
  <artifactId>order-invoice-sdk</artifactId>
  <packaging>jar</packaging>
  <version>1.0.0-SNAPSHOT</version>

  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
    <lombok.version>1.18.8</lombok.version>
    <yxt.order-types.version>2.5.1-SNAPSHOT</yxt.order-types.version>
  </properties>


  <dependencies>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>${lombok.version}</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.hibernate.validator</groupId>
      <artifactId>hibernate-validator</artifactId>
      <version>6.0.16.Final</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.yxt</groupId>
      <artifactId>yxt-common-lang</artifactId>
    </dependency>

    <dependency>
      <groupId>com.yxt.order.types</groupId>
      <artifactId>order-types</artifactId>
      <version>${yxt.order-types.version}</version>
    </dependency>
  </dependencies>

  <repositories>
    <repository>
      <id>aliyun</id>
      <name>aliyun</name>
      <url>https://maven.aliyun.com/repository/public</url>
    </repository>

    <repository>
      <id>hydee</id>
      <name>hydee</name>
      <url>https://nexus.hxyxt.com/repository/maven-public/</url>
    </repository>
  </repositories>

  <distributionManagement>
    <repository>
      <id>local-releases</id>
      <name>Nexus Release Repository</name>
      <url>https://nexus.hxyxt.com/repository/releases/</url>
    </repository>
    <snapshotRepository>
      <id>local-snapshots</id>
      <name>Nexus Snapshot Repository</name>
      <url>https://nexus.hxyxt.com/repository/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>

</project>