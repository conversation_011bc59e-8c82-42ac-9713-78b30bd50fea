CREATE TABLE `invoice_main`  (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `company_code` varchar(20)  DEFAULT NULL COMMENT '分公司编码',
    `company_name` varchar(20)  DEFAULT NULL COMMENT '分公司名称',
    `organization_code` varchar(20)  DEFAULT NULL COMMENT '所属机构编码',
    `organization_name` varchar(50)  DEFAULT NULL COMMENT '所属机构名称',
    `invoice_main_no` varchar(50)  DEFAULT NULL COMMENT '开票单号',
    `third_platform_code` varchar(20)  DEFAULT NULL COMMENT '平台编码',
    `third_order_no` varchar(50)  DEFAULT NULL COMMENT '第三方平台订单号',
    `order_no` varchar(50)  DEFAULT NULL COMMENT '订单号',
    `pos_no` varchar(50)  DEFAULT NULL COMMENT 'Pos系统销售单号',
    `user_id` varchar(50)   DEFAULT NULL COMMENT '会员编号',
    `mer_code` varchar(20)  DEFAULT NULL COMMENT '商户编码',
    `transaction_channel` varchar(20)  DEFAULT NULL COMMENT '交易场景 online:代表线上交易 ,offline:代表线下交易',
    `provider_code` varchar(20)  DEFAULT NULL COMMENT '供应方编码',
    `invoice_code` varchar(50)  DEFAULT NULL COMMENT '发票代码（税务云回调写入）',
    `invoice_no` varchar(50)  DEFAULT NULL COMMENT '发票号码（税务云回调写入）',
    `invoice_red_blue_type` varchar(20)  DEFAULT NULL COMMENT '蓝票:Tax_Invoice 红票:Credit_Note',
    `red_invoice_main_no` varchar(50)  DEFAULT NULL COMMENT '红冲对应原发票号,红票必填',
    `red_invoice_reason` varchar(50)  DEFAULT NULL COMMENT '红冲原因',
    `invoice_type` varchar(20)  DEFAULT NULL COMMENT '专票:Special_Invoice 普票:Ordinary_Invoice',
    `invoice_status` varchar(20)  DEFAULT NULL COMMENT '状态: WAIT-待开票; PROCESS-开票中;  FAIL-失败;    SUCCESS-成功;  RED_SUCCESS -红冲成功; ',
    `sync_status` varchar(20)  DEFAULT NULL COMMENT '状态: WAIT-待处理; DONE-已回传平台;  ',
    `actual_pay_amount` decimal(16, 6) DEFAULT NULL COMMENT '实付金额',
    `delivery_amount` decimal(16, 6) DEFAULT NULL COMMENT '配送费',
    `delivery_type` varchar(20)  DEFAULT NULL COMMENT '配送方式 平台配送:PlatformFulfillment 商家自配:MerchantFulfillment',
    `invoice_amount` decimal(16, 6) DEFAULT NULL COMMENT '金额 ',
    `tax_amount` decimal(16, 6) DEFAULT NULL COMMENT '税额 ',
    `price_tax_amount` decimal(16, 6) DEFAULT NULL COMMENT '价税合计 ',
    `split_bill`  varchar(20)  DEFAULT NULL COMMENT  '拆票标记   SPLIT-拆  NOT-不拆',
    `order_created` datetime NULL DEFAULT NULL COMMENT '订单创单时间',
    `pdf_url` varchar(200)  DEFAULT NULL COMMENT '电子发票PDF地址',
    `invoice_err_msg` text  COMMENT '失败原因 ',
    `apply_time` datetime DEFAULT NULL COMMENT '申请开票时间',
    `operator`   varchar(50)   DEFAULT NULL COMMENT '开票人',
    `payee`  varchar(50)  DEFAULT NULL COMMENT  '收款人',
    `reviewed`  varchar(50)  DEFAULT NULL COMMENT  '复核人',
    `apply_channel` varchar(20)  DEFAULT NULL     COMMENT '申请渠道 一心到家-YXDJ 心云-XY 海典H2-H2POS',
    `seller_number` varchar(50)   DEFAULT NULL COMMENT '开票主体',
    `seller_name` varchar(50)   DEFAULT NULL COMMENT '开票主体名称',
    `seller_tin` varchar(50)   DEFAULT NULL COMMENT '开票纳税人识别号',
    `seller_address`  varchar(100)   DEFAULT NULL COMMENT '开票主体地址',
    `seller_phone`  varchar(50)   DEFAULT NULL COMMENT '开票主体电话',
    `seller_bank`  varchar(50)   DEFAULT NULL COMMENT '开票主体银行',
    `seller_bank_account`  varchar(50)   DEFAULT NULL COMMENT '开票主体银行账户',
    `buyer_party_type` varchar(20)   DEFAULT NULL COMMENT '购方类型 个人-Individual  单位-Organization',
    `buyer_name` varchar(50)   DEFAULT NULL COMMENT '购方名字',
    `buyer_tin` varchar(50)   DEFAULT NULL COMMENT '购方个人身份证\单位纳税人识别号必填.',
    `buyer_address`  varchar(100)   DEFAULT NULL COMMENT '购方地址',
    `buyer_phone`  varchar(50)   DEFAULT NULL COMMENT '购方电话',
    `buyer_bank`  varchar(50)   DEFAULT NULL COMMENT '购方银行,专票必填',
    `buyer_bank_account`  varchar(50)   DEFAULT NULL COMMENT '购方银行账户,专票必填',
    `buyer_email`  varchar(50)   DEFAULT NULL COMMENT '购方邮箱',
    `buyer_mobile`  varchar(50)   DEFAULT NULL COMMENT '购方手机号',
    `show_buyer_bank_account`  varchar(20)   DEFAULT NULL COMMENT '显示购方银行账户 SHOW-显示 HIDE-不显示',
    `is_valid` bigint NOT NULL DEFAULT '0' COMMENT '是否起效 1-起效 -1-未起效  ',
    `provider_param`  varchar(100)   DEFAULT NULL COMMENT '供应商参数',
    `created` datetime NULL DEFAULT NULL COMMENT '平台创建时间',
    `updated` datetime NULL DEFAULT NULL COMMENT '平台更新时间',
    `created_by` varchar(50)  DEFAULT NULL COMMENT '创建人',
    `updated_by` varchar(50)  DEFAULT NULL COMMENT '更新人',
    `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',
    `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',
    `version` bigint NULL DEFAULT NULL COMMENT '数据版本，每次update+1',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_invoice_main_no` (`invoice_main_no`) USING BTREE,
    KEY `idx_user_id` (`user_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic COMMENT='心云发票单' ;



CREATE TABLE `invoice_detail`  (
     `id` bigint NOT NULL AUTO_INCREMENT,
     `invoice_main_no` varchar(50)  DEFAULT NULL COMMENT '开票单号',
     `invoice_detail_no` varchar(50)  DEFAULT NULL COMMENT '开票单明细号',
     `row_no` varchar(50)  DEFAULT NULL COMMENT '行号',
     `tax_classification_code`  varchar(50)  DEFAULT NULL COMMENT '税收分类编码',
     `top_level_tax_classification_code`  varchar(50)  DEFAULT NULL COMMENT '税收分类编码父级分类名称',
     `erp_code` varchar(20)  DEFAULT NULL COMMENT '商品编码',
     `erp_name` varchar(255)  DEFAULT NULL COMMENT '商品名称',
     `commodity_count` decimal(16, 6) DEFAULT NULL COMMENT '商品数量',
     `commodity_spec` varchar(255)  DEFAULT NULL COMMENT '商品规格',
     `unit` varchar(20)  DEFAULT NULL COMMENT '单位',
     `price` decimal(16, 6) DEFAULT NULL COMMENT '商品售价',
     `total_amount` decimal(16, 6) DEFAULT NULL COMMENT '商品总额=price*数量',
     `tax_amount` decimal(16, 6) DEFAULT NULL COMMENT '行税额行金额/(1+税率)*税率',
     `tax_rate` decimal(16, 6) DEFAULT NULL COMMENT '税率',
     `tax_rate_code`  varchar(20)  DEFAULT NULL COMMENT '税率编码',
     `price_tax_amount` decimal(16, 6) DEFAULT NULL COMMENT '价税合计 ',
     `invoice_line_type` varchar(20)  DEFAULT NULL COMMENT '行性质 Regular_Line-正常行  Discount_Line-折扣行  Discounted_Line-被折扣行',
     `discount_amount` decimal(16, 6) DEFAULT NULL COMMENT '抵扣金额',
     `policy_status` varchar(10)  DEFAULT NULL COMMENT '启动优惠政策 YES-启用  NO-不启用',
     `policy_tag` varchar(10)  DEFAULT NULL COMMENT '优惠标识',
     `policy_tax_rate` decimal(16, 6) DEFAULT NULL COMMENT '优惠税率',
     `is_valid` bigint NOT NULL DEFAULT '0' COMMENT '是否起效 1-起效 -1-未起效  ',
     `created` datetime NULL DEFAULT NULL COMMENT '平台创建时间',
     `updated` datetime NULL DEFAULT NULL COMMENT '平台更新时间',
     `created_by` varchar(50)  DEFAULT NULL COMMENT '创建人',
     `updated_by` varchar(50)  DEFAULT NULL COMMENT '更新人',
     `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',
     `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',
     `version` bigint NULL DEFAULT NULL COMMENT '数据版本，每次update+1',
     PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic COMMENT='心云发票单明细' ;













-- 生成 invoice_main 分表（64张）
DELIMITER $$
CREATE PROCEDURE GenerateInvoiceMainTables()
BEGIN
    DECLARE i INT DEFAULT 0;
    WHILE i < 64 DO
        SET @table_name = CONCAT('invoice_main_', i);
        SET @sql = CONCAT(
            'CREATE TABLE `', @table_name, '` (',
            '  `id` bigint NOT NULL AUTO_INCREMENT,',
            '  `company_code` varchar(20)  DEFAULT NULL COMMENT ''分公司编码'',',
            '  `company_name` varchar(20)  DEFAULT NULL COMMENT ''分公司名称'',',
            '  `organization_code` varchar(20)  DEFAULT NULL COMMENT ''所属机构编码'',',
            '  `organization_name` varchar(50)  DEFAULT NULL COMMENT ''所属机构名称'',',
            '  `invoice_main_no` varchar(50) DEFAULT NULL COMMENT ''开票单号'',',
            '  `third_platform_code` varchar(20) DEFAULT NULL COMMENT ''平台编码'',',
            '  `third_order_no` varchar(50) DEFAULT NULL COMMENT ''第三方平台订单号'',',
            '  `order_no` varchar(50) DEFAULT NULL COMMENT ''订单号'',',
            '  `pos_no` varchar(50) DEFAULT NULL COMMENT ''pos销售单号'',',
            '  `user_id` varchar(50)  DEFAULT NULL COMMENT ''会员编号'',',
            '  `mer_code` varchar(20)  DEFAULT NULL COMMENT ''商户编码'',',
            '  `transaction_channel` varchar(20) DEFAULT NULL COMMENT ''交易场景 online:线上交易, offline:线下交易'',',
            '  `provider_code` varchar(20) DEFAULT NULL COMMENT ''供应方编码'',',
            '  `invoice_code` varchar(50) DEFAULT NULL COMMENT ''发票代码（税务云回调写入）'',',
            '  `invoice_no` varchar(50) DEFAULT NULL COMMENT ''发票号码（税务云回调写入）'',',
            '  `invoice_red_blue_type` varchar(20) DEFAULT NULL COMMENT ''蓝票:Tax_Invoice 红票:Credit_Note'',',
            '  `red_invoice_main_no` varchar(50) DEFAULT NULL COMMENT ''红冲对应原发票号,红票必填'',',
            '  `red_invoice_reason` varchar(50)  DEFAULT NULL COMMENT ''红冲原因'',',
            '  `invoice_type` varchar(20) DEFAULT NULL COMMENT ''专票:Special_Invoice 普票:Ordinary_Invoice'',',
            '  `invoice_status` varchar(20) DEFAULT NULL COMMENT ''状态: WAIT-待开票; PROCESS-开票中; FAIL-失败; SUCCESS-成功; RED_SUCCESS-红冲成功'',',
            '  `sync_status` varchar(20) DEFAULT NULL COMMENT ''状态: WAIT-待处理; DONE-已回传平台'',',
            '  `actual_pay_amount` decimal(16,6) DEFAULT NULL COMMENT ''实付金额'',',
            '  `delivery_amount` decimal(16,6) DEFAULT NULL COMMENT ''配送费'',',
            '  `delivery_type` varchar(20) DEFAULT NULL COMMENT ''配送方式 PlatformFulfillment:平台配送 MerchantFulfillment:商家自配'',',
            '  `invoice_amount` decimal(16,6) DEFAULT NULL COMMENT ''金额'',',
            '  `tax_amount` decimal(16,6) DEFAULT NULL COMMENT ''税额'',',
            '  `price_tax_amount` decimal(16,6) DEFAULT NULL COMMENT ''价税合计'',',
            '  `split_bill` varchar(20) DEFAULT NULL COMMENT ''拆票标记 SPLIT-拆 NOT-不拆'',',
            '  `order_created` datetime DEFAULT NULL COMMENT ''订单创单时间'',',
            '  `pdf_url` varchar(200) DEFAULT NULL COMMENT ''电子发票PDF地址'',',
            '  `invoice_err_msg` text  COMMENT ''失败原因'',',
            '  `apply_time` datetime DEFAULT NULL COMMENT ''申请开票时间'',',
            '  `operator` varchar(50)  DEFAULT NULL COMMENT ''开票人'',',
            '  `payee` varchar(50) DEFAULT NULL COMMENT ''收款人'',',
            '  `reviewed` varchar(50) DEFAULT NULL COMMENT ''复核人'',',
            '  `apply_channel` varchar(20)  DEFAULT NULL COMMENT ''申请渠道 一心到家-YXDJ 心云-XY 海典H2-H2POS'',',
            '  `seller_number` varchar(50)  DEFAULT NULL COMMENT ''开票主体'',',
            '  `seller_name` varchar(50)  DEFAULT NULL COMMENT ''开票主体名称'',',
            '  `seller_tin` varchar(50)  DEFAULT NULL COMMENT ''开票纳税人识别号'',',
            '  `seller_address` varchar(100)  DEFAULT NULL COMMENT ''开票主体地址'',',
            '  `seller_phone` varchar(50)  DEFAULT NULL COMMENT ''开票主体电话'',',
            '  `seller_bank` varchar(50)  DEFAULT NULL COMMENT ''开票主体银行'',',
            '  `seller_bank_account` varchar(50)  DEFAULT NULL COMMENT ''开票主体银行账户'',',
            '  `buyer_party_type` varchar(20)  DEFAULT NULL COMMENT ''购方类型 个人-Individual 单位-Organization'',',
            '  `buyer_name` varchar(50)  DEFAULT NULL COMMENT ''购方名字'',',
            '  `buyer_tin` varchar(50)  DEFAULT NULL COMMENT ''购方个人身份证\\单位纳税人识别号'',',
            '  `buyer_address` varchar(100)  DEFAULT NULL COMMENT ''购方地址'',',
            '  `buyer_phone` varchar(50)  DEFAULT NULL COMMENT ''购方电话'',',
            '  `buyer_bank` varchar(50)  DEFAULT NULL COMMENT ''购方银行,专票必填'',',
            '  `buyer_bank_account` varchar(50)  DEFAULT NULL COMMENT ''购方银行账户,专票必填'',',
            '  `buyer_email` varchar(50)  DEFAULT NULL COMMENT ''购方邮箱'',',
            '  `buyer_mobile` varchar(50)  DEFAULT NULL COMMENT ''购方手机号'',',
            '  `show_buyer_bank_account` varchar(20)  DEFAULT NULL COMMENT ''显示购方银行账户 SHOW-显示 HIDE-不显示'',',
            '  `is_valid` bigint NOT NULL DEFAULT ''0'' COMMENT ''是否起效 1-起效 -1-未起效'',',
            '  `provider_param`  varchar(100)   DEFAULT NULL COMMENT ''供应商参数'',',
            '  `created` datetime DEFAULT NULL COMMENT ''平台创建时间'',',
            '  `updated` datetime DEFAULT NULL COMMENT ''平台更新时间'',',
            '  `created_by` varchar(50) DEFAULT NULL COMMENT ''创建人'',',
            '  `updated_by` varchar(50) DEFAULT NULL COMMENT ''更新人'',',
            '  `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT ''系统创建时间'',',
            '  `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''系统更新时间'',',
            '  `version` bigint DEFAULT NULL COMMENT ''数据版本，每次update+1'',',
            '  PRIMARY KEY (`id`),',
            '  UNIQUE KEY `idx_invoice_main_no` (`invoice_main_no`),',
            '  KEY `idx_user_id` (`user_id`)',
            ') ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ',
            'ROW_FORMAT=DYNAMIC COMMENT=''心云发票单-分表'';'
        );
PREPARE stmt FROM @sql;
EXECUTE stmt;
SET i = i + 1;
END WHILE;
END
$$
DELIMITER ;
CALL GenerateInvoiceMainTables();





-- 生成 invoice_detail 分表（256张）
DELIMITER $$
CREATE PROCEDURE GenerateInvoiceDetailTables()
BEGIN
    DECLARE i INT DEFAULT 0;
    WHILE i < 64 DO
        SET @table_name = CONCAT('invoice_detail_', i);
        SET @sql = CONCAT(
            'CREATE TABLE `', @table_name, '` (',
            '  `id` bigint NOT NULL AUTO_INCREMENT,',
            '  `invoice_main_no` varchar(50) DEFAULT NULL COMMENT ''开票单号'',',
            '  `invoice_detail_no` varchar(50) DEFAULT NULL COMMENT ''开票单明细号'',',
            '  `row_no` varchar(50) DEFAULT NULL COMMENT ''行号'',',
            '  `tax_classification_code` varchar(50) DEFAULT NULL COMMENT ''税收分类编码'',',
            '  `top_level_tax_classification_code` varchar(50) DEFAULT NULL COMMENT ''税收分类编码父级分类名称'',',
            '  `erp_code` varchar(20) DEFAULT NULL COMMENT ''商品编码'',',
            '  `erp_name` varchar(255) DEFAULT NULL COMMENT ''商品名称'',',
            '  `commodity_count` decimal(16,6) DEFAULT NULL COMMENT ''商品数量'',',
            '  `commodity_spec` varchar(255) DEFAULT NULL COMMENT ''商品规格'',',
            '  `unit` varchar(20) DEFAULT NULL COMMENT ''单位'',',
            '  `price` decimal(16,6) DEFAULT NULL COMMENT ''商品售价'',',
            '  `total_amount` decimal(16,6) DEFAULT NULL COMMENT ''商品总额=price*数量'',',
            '  `tax_amount` decimal(16,6) DEFAULT NULL COMMENT ''行税额行金额/(1+税率)*税率'',',
            '  `tax_rate` decimal(16,6) DEFAULT NULL COMMENT ''税率'',',
            '  `tax_rate_code` varchar(20) DEFAULT NULL COMMENT ''税率编码'',',
            '  `price_tax_amount` decimal(16,6) DEFAULT NULL COMMENT ''价税合计'',',
            '  `invoice_line_type` varchar(20) DEFAULT NULL COMMENT ''行性质 Regular_Line-正常行 Discount_Line-折扣行 Discounted_Line-被折扣行'',',
            '  `discount_amount` decimal(16,6) DEFAULT NULL COMMENT ''抵扣金额'',',
            '  `policy_status` varchar(10) DEFAULT NULL COMMENT ''启动优惠政策 YES-启用 NO-不启用'',',
            '  `policy_tag` varchar(10) DEFAULT NULL COMMENT ''优惠标识'',',
            '  `policy_tax_rate` decimal(16,6) DEFAULT NULL COMMENT ''优惠税率'',',
            '  `is_valid` bigint NOT NULL DEFAULT ''0'' COMMENT ''是否起效 1-起效 -1-未起效'',',
            '  `created` datetime DEFAULT NULL COMMENT ''平台创建时间'',',
            '  `updated` datetime DEFAULT NULL COMMENT ''平台更新时间'',',
            '  `created_by` varchar(50) DEFAULT NULL COMMENT ''创建人'',',
            '  `updated_by` varchar(50) DEFAULT NULL COMMENT ''更新人'',',
            '  `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT ''系统创建时间'',',
            '  `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''系统更新时间'',',
            '  `version` bigint DEFAULT NULL COMMENT ''数据版本，每次update+1'',',
            '  PRIMARY KEY (`id`),',
            '  KEY `idx_main_no` (`invoice_main_no`)',
            ') ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ',
            'ROW_FORMAT=DYNAMIC COMMENT=''心云发票单明细-分表'';'
        );
PREPARE stmt FROM @sql;
EXECUTE stmt;
SET i = i + 1;
END WHILE;
END
$$
DELIMITER ;
CALL GenerateInvoiceDetailTables();