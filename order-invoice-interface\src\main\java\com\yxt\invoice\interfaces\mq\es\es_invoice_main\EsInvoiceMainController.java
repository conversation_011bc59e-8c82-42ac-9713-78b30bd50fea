package com.yxt.invoice.interfaces.mq.es.es_invoice_main;

import static com.yxt.invoice.interfaces.config.ThreadPoolConfig.COMMON_BUSINESS_POOL;

import com.yxt.common.logic.flash.AbstractFlashData;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.invoice.infrastructure.db.es.InvoiceScene;
import com.yxt.invoice.infrastructure.db.es.es_invoice_main.mapper.EsInvoiceMainMapper;
import com.yxt.invoice.interfaces.mq.es.es_invoice_main.req.FlashOfflineOrderManageDataReq;
import com.yxt.lang.util.JsonUtils;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 发票控制器 实现SDK接口，提供RESTful API
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@RestController
@RequestMapping("/1.0/es_invoice_main")
@Validated
@Slf4j
public class EsInvoiceMainController {

  @Resource
  private EsInvoiceMainMapper esInvoiceMainMapper;

  @Resource
  private List<AbstractFlashData<?, ?, InvoiceScene>> invoiceSceneList;

  @Qualifier(COMMON_BUSINESS_POOL)
  @Resource
  private ThreadPoolExecutor commonBusinessPool;

  @PostMapping("/createIndex")
  public Boolean createIndex() {
    return esInvoiceMainMapper.createIndex();
  }

  @PostMapping("/es-invoice/flashDataToEs")
  public Boolean flashEsInvoice(@RequestBody @Valid FlashOfflineOrderManageDataReq req) {

    for (AbstractFlashData<?, ?, InvoiceScene> abstractFlash : invoiceSceneList) {

      Date startDate = req.getStartDate();
      Date endDate = req.getEndDate();
      List<String> noList = req.getNoList();

      commonBusinessPool.submit(() -> {
        try {
          log.info("flush task running,{},req:{}", abstractFlash.getClass().getName(),
              JsonUtils.toJson(req));
          FlashParam flashParam = new FlashParam();
          flashParam.setStart(startDate);
          flashParam.setEnd(endDate);
          flashParam.setNoList(noList);
          flashParam.setMonitorKey(req.getMonitorKey());
          abstractFlash.startFlush(flashParam);
          log.info("flush task done,{},req:{}", abstractFlash.getClass().getName(),
              JsonUtils.toJson(flashParam));
        } catch (Exception e) {
          log.error("flashEsInvoice flash error", e);
        }
      });
    }

    return Boolean.TRUE;


  }

}
