package com.yxt.invoice.sdk.dto.req;

import com.google.common.base.Preconditions;
import com.yxt.invoice.sdk.dto.InvoiceAmountDTO;
import com.yxt.lang.dto.api.MiddleRequestBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SimpleApplyInvoiceMainReqDto extends MiddleRequestBase implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户编码
     */
    @ApiModelProperty("商户编码,必填")
    private String merCode;


    /**
     * 订单号
     */
    @ApiModelProperty("订单号,必填")
    private String orderNo;

    /**
     * 业务类型 O2O, B2C
     */
    @ApiModelProperty("业务类型 O2O、B2C, 线下单填O2O , 必填 ")
    private String businessType;


    /**
     * 交易场景 online:线上交易, offline:线下交易
     */
    @ApiModelProperty("交易场景 online:线上交易, offline:线下交易,必填")
    private String transactionChannel;


    /**
     * 操作人
     */
    @ApiModelProperty("操作人,必填")
    private String operatorUserId;



    /**
     * 会员编号
     */
    @ApiModelProperty("会员编号,必填")
    private String userId;

    /**
     * 发票类型
     * 蓝票必填
     */
    @ApiModelProperty("发票类型,专票:Special_Invoice 普票:Ordinary_Invoice ,必填")
    private String invoiceType;


    /**
     * 备注
     */
    @ApiModelProperty("备注,可选")
    private String notes;


    /**
     * 申请开票时间
     */
    @ApiModelProperty("申请开票时间,必填")
    private LocalDateTime applyTime;


    /**
     * 申请渠道 一心到家-YXDJ 心云-XY 海典H2-H2POS
     */
    @ApiModelProperty("申请渠道 一心到家:YXDJ 心云:XY 海典H2:H2POS,必填")
    private String applyChannel;


    /**
     * 购方类型
     */
    @ApiModelProperty("购方类型 个人:Individual  单位:Organization,必填")
    private String buyerPartyType;

    /**
     * 购方名称
     */
    @ApiModelProperty("购方名称,必填")
    private String buyerName;

    /**
     * 购方税号（个人身份证/单位纳税人识别号）
     */
    @ApiModelProperty("购方税号（个人身份证/单位纳税人识别号）,必填")
    private String buyerTin;

    /**
     * 购方地址
     */
    @ApiModelProperty("购方地址,可选")
    private String buyerAddress;

    /**
     * 购方电话
     */
    @ApiModelProperty("购方电话,可选")
    private String buyerPhone;

    /**
     * 购方银行
     */
    @ApiModelProperty("购方银行,可选")
    private String buyerBank;

    /**
     * 购方银行账户
     */
    @ApiModelProperty("购方银行账户,可选")
    private String buyerBankAccount;

    /**
     * 购方邮箱
     */
    @ApiModelProperty("购方邮箱,可选")
    private String buyerEmail;

    /**
     * 购方手机号
     */
    @ApiModelProperty("购方手机号,可选")
    private String buyerMobile;

    /**
     * 显示购方银行账户 SHOW-显示 HIDE-不显示
     */
    @ApiModelProperty("发票是否显示购方银行账户 SHOW-显示 HIDE-不显示,必填默认HIDE")
    private String showBuyerBankAccount;

    @ApiModelProperty("发票开具金额")
    private InvoiceAmountDTO invoiceAmount;


    public void checkValid() {
        Preconditions.checkArgument(StringUtils.isNotBlank(merCode), "商户编码不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(orderNo), "订单号不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(businessType) && ("O2O,B2C").contains(businessType), "业务类型不能为空且需合法");
        Preconditions.checkArgument(StringUtils.isNotBlank(transactionChannel) && ("online,offline").contains(businessType), "交易场景不能为空且需合法");
        Preconditions.checkArgument(StringUtils.isNotBlank(userId), "会员编号不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(invoiceType) && ("Special_Invoice,Ordinary_Invoice").contains(invoiceType), "发票类型不能为空且需合法");
        Preconditions.checkArgument(null != applyTime, "申请开票时间不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(applyChannel) && ("YXDJ,XY,H2POS").contains(applyChannel), "申请渠道不能为空且需合法");
        Preconditions.checkArgument(StringUtils.isNotBlank(buyerPartyType) && ("Individual,Organization").contains(buyerPartyType), "购方类型不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(buyerName), "购方名称不能为空");
        Preconditions.checkArgument(!buyerName.contains(" "), "购方名称不能包含空格");
        Preconditions.checkArgument(buyerName.matches("^[\\u4e00-\\u9fa5a-zA-Z]+$"), "购方名称只能包含中文和英文字母，不能包含空格及特殊字符");


        Preconditions.checkArgument(StringUtils.isNotBlank(buyerTin), "购方税号不能为空");
        Preconditions.checkArgument(!buyerTin.contains(" "), "购方税号不能包含空格");
        Preconditions.checkArgument(buyerTin.equals(buyerTin.toUpperCase()), "购方税号必须为大写");
        if (StringUtils.isNotEmpty(buyerPhone)) {
            Preconditions.checkArgument(buyerPhone.matches("^(0\\d{2,3}-)?\\d{7,8}(-\\d{1,6})?$|^1[3-9]\\d{9}$"), "购方电话格式不正确");
        }

        if (StringUtils.isNotEmpty(buyerBankAccount)) {
            String cleanBankAccount = buyerBankAccount.replaceAll("\\s+", "");
            // 银行卡号长度通常为16-19位数字
            Preconditions.checkArgument(cleanBankAccount.matches("^\\d{16,19}$"), "购方银行账户格式不正确，应为16-19位数字");
        }


        if (StringUtils.isNotEmpty(buyerEmail)) {
            Preconditions.checkArgument(buyerEmail.matches("^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"),
                    "购方邮箱格式不正确");
        }

        if (StringUtils.isNotEmpty(buyerMobile)) {
            Preconditions.checkArgument(buyerMobile.matches("^(0\\d{2,3}-)?\\d{7,8}(-\\d{1,6})?$|^1[3-9]\\d{9}$"), "购方电话格式不正确");

        }

        Preconditions.checkArgument(null != invoiceAmount, "发票开具金额方式不能为空");

    }

}
