package com.yxt.invoice.domain.event.create;

import com.yxt.invoice.domain.event.BaseCreateInvoiceDomainEvent;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 发票申请事件
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public class RedInvoiceApplyEvent extends BaseCreateInvoiceDomainEvent<RedInvoiceApplyEvent.Data> {

    public static final String TYPE = "RedInvoiceApplyEvent";

    public RedInvoiceApplyEvent(InvoiceAggregate aggregate) {
        super(aggregate, null, TYPE,new Data(aggregate));
    }

    @Getter
    @Setter
    @ToString(callSuper = true)
    @NoArgsConstructor
    public static class Data extends BaseCreateInvoiceDomainEvent.BaseData {


        Data(InvoiceAggregate aggregate) {
            super.convert(aggregate);
        }
    }
}
