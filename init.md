# 医药电商开票中心模块 - DDD自然语言代码生成指南

## 项目架构说明
采用DDD经典四层架构：Controller/Application/Domain/Infrastructure
技术栈：Spring Boot + MyBatis
数据库：order_invoice，包含invoice_main主表和invoice_detail明细表 已建立链接可自动扫描
文件编码格式: UTF-8

## 业务需求
为医药连锁电商平台C端用户提供订单开票相关接口服务：
1. 申请开票
2. 发票列表查询  
3. 发票详情查询
4. 发票红冲

## 代码生成描述指南

### Controller层代码生成描述

**发票控制器(InvoiceController)**
创建一个RESTful风格的发票控制器，包含以下特征：
- 使用@RestController注解，路径为/api/v1/invoices
- 添加@Validated注解用于参数验证
- 包含日志记录功能
- 注入InvoiceApplicationService应用服务
- 实现统一的用户身份获取方法，从请求中提取当前用户ID
- 所有方法返回统一的ApiResponse格式

**四个接口方法描述：**

1. **申请开票接口**：POST /apply
   - 接收InvoiceApplicationRequest请求体，包含订单ID、发票类型、发票抬头、税号、发票明细列表
   - 获取当前用户ID，构建InvoiceApplicationCommand命令对象
   - 调用应用服务的applyInvoice方法
   - 返回申请结果，包含发票ID和状态

2. **发票列表查询接口**：POST /list
   - 接收InvoiceListRequest查询参数，包含状态筛选、分页参数
   - 构建InvoiceListQuery查询对象
   - 调用应用服务的queryInvoiceList方法
   - 返回分页的发票列表

3. **发票详情查询接口**：POST /{invoiceId}
   - 接收路径参数invoiceId
   - 构建InvoiceDetailQuery查询对象，包含发票ID和用户ID
   - 调用应用服务的queryInvoiceDetail方法
   - 返回详细的发票信息和明细列表

4. **发票红冲接口**：POST /{invoiceId}/red-credit
   - 接收路径参数invoiceId和请求体InvoiceRedCreditRequest（包含红冲原因）
   - 构建InvoiceRedCreditCommand命令对象
   - 调用应用服务的redCreditInvoice方法
   - 返回红冲结果

**请求响应DTO描述：**
创建对应的请求响应对象，包含JSR-303验证注解，如@NotBlank、@NotNull、@Valid等，设置合适的错误提示信息和长度限制。

### Application层代码生成描述

**发票应用服务(InvoiceApplicationService)**
创建事务性的应用服务类，特征如下：
- 使用@Service和@Transactional注解
- 添加日志功能
- 注入InvoiceRepository仓储、InvoiceDomainService领域服务、OrderService订单服务
- 包含私有的DTO转换方法

**四个业务方法描述：**

1. **申请开票方法(applyInvoice)**
   - 验证订单存在性和用户权限
   - 检查订单是否已申请过发票，避免重复申请
   - 调用领域服务进行业务规则验证
   - 创建InvoiceMain聚合根，设置基本信息和待开票状态
   - 根据请求明细创建InvoiceDetail实体列表
   - 通过聚合根添加明细的方法关联明细
   - 保存到仓储并返回结果

2. **发票列表查询方法(queryInvoiceList)**
   - 使用PageHelper进行分页处理
   - 调用仓储查询用户的发票列表，支持状态筛选
   - 转换为DTO对象返回分页结果

3. **发票详情查询方法(queryInvoiceDetail)**
   - 根据发票ID和用户ID查询发票，验证权限
   - 如果不存在则抛出业务异常
   - 转换为详情结果对象，包含完整的发票信息和明细列表

4. **发票红冲方法(redCreditInvoice)**
   - 查询发票并验证用户权限
   - 通过聚合根的canRedCredit方法验证是否可以红冲
   - 调用聚合根的redCredit方法执行红冲逻辑
   - 保存更新后的发票状态

**命令查询对象(CQRS)**
创建对应的Command和Query对象，使用Builder模式，包含必要的业务参数。创建Result结果对象返回处理结果。

### Domain层代码生成描述

**发票聚合根(InvoiceMainAggregate)**
创建发票主实体，作为聚合根，包含以下特征：
- 包含所有发票基本属性：ID、订单ID、用户ID、发票类型、发票抬头、税号、发票状态、总金额、红冲原因、创建时间、更新时间
- 包含发票明细列表属性
- 实现关键的领域方法：
  - addDetails：添加明细并设置关联关系
  - markAsIssued：标记为已开票，验证状态转换规则
  - redCredit：执行红冲，验证业务规则并更新状态
  - canRedCredit：判断是否可以红冲
  - 状态判断方法：isPending、isIssued、isRedCredited

**发票明细实体(InvoiceDetail)**
创建发票明细实体，包含：
- 基本属性：ID、发票ID、商品ID、商品名称、单价、数量、金额
- 业务方法：
  - calculateAmount：计算明细金额
  - validateAmount：验证金额正确性

**值对象和枚举**
创建InvoiceType发票类型枚举（个人、企业）和InvoiceStatus发票状态枚举（待开票、已开票、已红冲），包含code和desc属性，提供fromCode静态方法用于转换。

**领域服务(InvoiceDomainService)**
创建领域服务，包含以下方法：
- validateInvoiceApplication：验证开票申请的业务规则，如企业发票必须有税号、明细不能为空、金额必须大于0等
- calculateTotalAmount：计算发票总金额
- validateTaxNumber：验证税号格式的合法性

**仓储接口(InvoiceRepository)**
定义仓储接口，包含以下方法签名：
- save：保存发票聚合根
- findById：根据ID查询
- findByIdAndUserId：根据ID和用户ID查询，用于权限验证
- findByUserId：查询用户的所有发票
- findByUserIdAndStatus：根据用户ID和状态查询，支持分页
- findByOrderId：根据订单ID查询，用于重复申请检查

### Infrastructure层代码生成描述

**仓储实现(InvoiceRepositoryImpl)**
实现InvoiceRepository接口，特征如下：
- 使用@Repository注解
- 注入InvoiceMainMapper和InvoiceDetailMapper
- 实现所有仓储接口方法，处理主表和明细表的关联操作
- 在save方法中处理新增和更新逻辑，保存主表后批量保存明细

**MyBatis Mapper接口**
创建InvoiceMainMapper和InvoiceDetailMapper接口：
- InvoiceMainMapper包含：insert、selectById、selectByIdAndUserId、selectByUserId、selectByUserIdAndStatus、selectByOrderId、updateById等方法
- InvoiceDetailMapper包含：batchInsert、selectByInvoiceId、deleteByInvoiceId等方法
- 使用@Param注解标注参数

**MyBatis XML映射文件**
为每个Mapper创建对应的XML映射文件，包含：
- 完整的resultMap定义，处理数据库字段到实体属性的映射
- 所有CRUD操作的SQL语句
- 支持动态SQL，如条件查询、批量操作等
- 处理一对多关联查询（发票主表和明细表）

## 异常处理描述
创建业务异常类BusinessException和领域异常类DomainException，继承RuntimeException。在Controller层添加全局异常处理器，统一处理业务异常并返回标准错误响应。

## 通用组件描述
- ApiResponse：统一响应格式，包含code、message、data字段
- PageResult：分页结果对象，包含list、total、pageNum、pageSize字段
- UserContext：用户上下文工具类，用于获取当前用户信息

## Cursor使用提示词模板

### 基础生成提示词
```
请基于DDD架构为医药电商开票中心模块生成[具体层级]的代码：

业务场景：[具体业务描述]
架构层级：Controller/Application/Domain/Infrastructure
技术栈：Spring Boot + MyBatis
数据库：invoice_main主表，invoice_detail明细表

要求：
1. 严格遵循DDD分层职责
2. 使用合适的设计模式和注解
3. 包含完整的参数验证和异常处理
4. 添加必要的业务逻辑和数据验证
5. 代码包含详细注释
6. 遵循阿里巴巴Java开发规范
```

### 分层专用提示词

**Controller层：**
"为医药电商开票中心创建RESTful风格的Controller，包含申请开票、发票查询、红冲等接口，需要参数验证、用户权限检查、统一响应格式"

**Application层：**
"创建事务性的应用服务，协调领域对象完成开票业务用例，包含业务流程编排、DTO转换、异常处理"

**Domain层：**
"设计发票聚合根和领域服务，包含开票业务规则、状态转换逻辑、领域验证，体现DDD核心业务概念"

**Infrastructure层：**
"实现MyBatis数据访问层，包含Mapper接口、XML映射文件、仓储实现，处理数据持久化和查询"

## 注意事项
1. 所有金额字段使用BigDecimal类型
2. 时间字段使用LocalDateTime类型
3. 枚举类型提供code和desc属性
4. 重要业务操作添加日志记录
5. 数据库操作考虑事务边界
6. 用户权限验证防止越权访问
7. 业务异常使用自定义异常类型