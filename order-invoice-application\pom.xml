<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.yxt.ddd</groupId>
    <artifactId>order-invoice-service</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

  <groupId>com.yxt.ddd.application</groupId>
  <artifactId>order-invoice-application</artifactId>
  <version>1.0.0</version>
  <packaging>jar</packaging>


  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
    <yxt.order-types.version>2.5.1-SNAPSHOT</yxt.order-types.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.yxt.ddd.domain</groupId>
      <artifactId>order-invoice-domain</artifactId>
      <version>1.0.0</version>
    </dependency>
    <dependency>
      <groupId>com.yxt.ddd.infrastructure</groupId>
      <artifactId>order-invoice-infrastructure</artifactId>
      <version>1.0.0</version>
    </dependency>


    <dependency>
      <groupId>com.yxt.order.types</groupId>
      <artifactId>order-types</artifactId>
      <version>${yxt.order-types.version}</version>
    </dependency>
    <!-- Spring Boot Starter -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>
    
    <!-- Spring Boot Validation -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
  </dependencies>


</project>