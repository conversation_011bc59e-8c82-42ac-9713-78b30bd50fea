package com.yxt.invoice.infrastructure.db.mysql.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 发票主表DO
 * 与数据库表invoice_main对应
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Data
@TableName("invoice_main")
public class InvoiceMainDO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 分公司编码
     */
    @TableField("company_code")
    private String companyCode;

    /**
     * 分公司名称
     */
    @TableField("company_name")
    private String companyName;

    /**
     * 机构编码
     */
    @TableField("organization_code")
    private String organizationCode;

    /**
     * 机构名称
     */
    @TableField("organization_name")
    private String organizationName;

    /**
     * 开票单号
     */
    @TableField("invoice_main_no")
    private String invoiceMainNo;

    /**
     * 第三方平台编码
     */
    @TableField("third_platform_code")
    private String thirdPlatformCode;

    /**
     * 第三方订单号
     */
    @TableField("third_order_no")
    private String thirdOrderNo;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * pos销售单号
     */
    @TableField("pos_no")
    private String posNo;

    /**
     * 会员编号
     */
    @TableField("user_id")
    private String userId;

    /**
     * 商户编码
     */
    @TableField("mer_code")
    private String merCode;

    /**
     * 交易渠道
     */
    @TableField("transaction_channel")
    private String transactionChannel;

    /**
     * 供应商编码
     */
    @TableField("provider_code")
    private String providerCode;

    /**
     * 发票代码
     */
    @TableField("invoice_code")
    private String invoiceCode;

    /**
     * 发票号码
     */
    @TableField("invoice_no")
    private String invoiceNo;

    /**
     * 红蓝字标识
     */
    @TableField("invoice_red_blue_type")
    private String invoiceRedBlueType;

    /**
     * 红冲发票单号
     */
    @TableField("red_invoice_main_no")
    private String redInvoiceMainNo;




    /**
     * 红冲原因
     * 01: 开票有误
     * 02: 销货退回
     * 03: 服务中止
     * 04: 销售折让
     */
    @TableField("red_invoice_reason")
    private String redInvoiceReason;

    /**
     *  备注
     */
    @TableField("notes")
    private String notes;

    /**
     * 发票类型
     */
    @TableField("invoice_type")
    private String invoiceType;

    /**
     * 发票状态
     */
    @TableField("invoice_status")
    private String invoiceStatus;

    /**
     * 同步状态
     */
    @TableField("sync_status")
    private String syncStatus;

    /**
     * 实付金额
     */
    @TableField("actual_pay_amount")
    private BigDecimal actualPayAmount;

    /**
     * 运费
     */
    @TableField("delivery_amount")
    private BigDecimal deliveryAmount;

    /**
     * 配送方式
     */
    @TableField("delivery_type")
    private String deliveryType;

    /**
     * 不含税金额
     */
    @TableField("invoice_amount")
    private BigDecimal invoiceAmount;

    /**
     * 税额
     */
    @TableField("tax_amount")
    private BigDecimal taxAmount;

    /**
     * 价税合计
     */
    @TableField("price_tax_amount")
    private BigDecimal priceTaxAmount;

    /**
     * 是否拆单
     */
    @TableField("split_bill")
    private String splitBill;

    /**
     * 订单创建时间
     */
    @TableField("order_created")
    private LocalDateTime orderCreated;

    /**
     * PDF地址
     */
    @TableField("pdf_url")
    private String pdfUrl;

    /**
     * 开票错误信息
     */
    @TableField("invoice_err_msg")
    private String invoiceErrMsg;

    /**
     * 申请时间
     */
    @TableField("apply_time")
    private LocalDateTime applyTime;

    /**
     * 操作员
     */
    @TableField("operator")
    private String operator;

    /**
     * 收款人
     */
    @TableField("payee")
    private String payee;

    /**
     * 复核人
     */
    @TableField("reviewed")
    private String reviewed;

    /**
     * 申请渠道
     */
    @TableField("apply_channel")
    private String applyChannel;

    /**
     * 销方编号
     */
    @TableField("seller_number")
    private String sellerNumber;

    /**
     * 销方名称
     */
    @TableField("seller_name")
    private String sellerName;

    /**
     * 销方纳税人识别号
     */
    @TableField("seller_tin")
    private String sellerTin;

    /**
     * 销方地址
     */
    @TableField("seller_address")
    private String sellerAddress;

    /**
     * 销方电话
     */
    @TableField("seller_phone")
    private String sellerPhone;

    /**
     * 销方开户行
     */
    @TableField("seller_bank")
    private String sellerBank;

    /**
     * 销方银行账户
     */
    @TableField("seller_bank_account")
    private String sellerBankAccount;

    /**
     * 购方类型
     */
    @TableField("buyer_party_type")
    private String buyerPartyType;

    /**
     * 购方名称
     */
    @TableField("buyer_name")
    private String buyerName;

    /**
     * 购方纳税人识别号
     */
    @TableField("buyer_tin")
    private String buyerTin;

    /**
     * 购方地址
     */
    @TableField("buyer_address")
    private String buyerAddress;

    /**
     * 购方电话
     */
    @TableField("buyer_phone")
    private String buyerPhone;

    /**
     * 购方开户行
     */
    @TableField("buyer_bank")
    private String buyerBank;

    /**
     * 购方银行账户
     */
    @TableField("buyer_bank_account")
    private String buyerBankAccount;

    /**
     * 购方邮箱
     */
    @TableField("buyer_email")
    private String buyerEmail;

    /**
     * 购方手机号
     */
    @TableField("buyer_mobile")
    private String buyerMobile;

    /**
     * 是否显示购方银行账户
     */
    @TableField("show_buyer_bank_account")
    private String showBuyerBankAccount;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Long isValid;


    /**
     * 供应商参数
     * List<ProviderParam></>
     */
    @TableField("provider_param")
    private String providerParam;

    /**
     * 创建时间
     */
    @TableField(value = "created", fill = FieldFill.INSERT)
    private LocalDateTime created;

    /**
     * 更新时间
     */
    @TableField(value = "updated", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updated;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 系统创建时间
     */
    @TableField("sys_create_time")
    private LocalDateTime sysCreateTime;

    /**
     * 系统更新时间
     */
    @TableField("sys_update_time")
    private LocalDateTime sysUpdateTime;

    /**
     * 数据版本
     */
    @Version
    @TableField("version")
    private Long version;


}
