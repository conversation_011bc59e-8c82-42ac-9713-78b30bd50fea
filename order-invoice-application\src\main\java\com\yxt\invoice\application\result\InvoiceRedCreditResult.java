package com.yxt.invoice.application.result;

/**
 * 发票红冲结果
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public class InvoiceRedCreditResult {

    /**
     * 发票ID
     */
    private Long invoiceId;

    /**
     * 开票单号
     */
    private String invoiceMainNo;

    /**
     * 红冲是否成功
     */
    private Boolean success;

    /**
     * 消息
     */
    private String message;

    /**
     * 发票状态
     */
    private String invoiceStatus;

    /**
     * 发票状态描述
     */
    private String invoiceStatusDesc;

    /**
     * 私有构造函数
     */
    private InvoiceRedCreditResult() {
    }

    /**
     * 成功结果
     */
    public static InvoiceRedCreditResult success(Long invoiceId, String invoiceMainNo, 
                                               String invoiceStatus, String invoiceStatusDesc) {
        InvoiceRedCreditResult result = new InvoiceRedCreditResult();
        result.invoiceId = invoiceId;
        result.invoiceMainNo = invoiceMainNo;
        result.invoiceStatus = invoiceStatus;
        result.invoiceStatusDesc = invoiceStatusDesc;
        result.success = true;
        result.message = "红冲成功";
        return result;
    }

    /**
     * 失败结果
     */
    public static InvoiceRedCreditResult failure(String message) {
        InvoiceRedCreditResult result = new InvoiceRedCreditResult();
        result.success = false;
        result.message = message;
        return result;
    }

    // Getters
    public Long getInvoiceId() {
        return invoiceId;
    }

    public String getInvoiceMainNo() {
        return invoiceMainNo;
    }

    public Boolean getSuccess() {
        return success;
    }

    public String getMessage() {
        return message;
    }

    public String getInvoiceStatus() {
        return invoiceStatus;
    }

    public String getInvoiceStatusDesc() {
        return invoiceStatusDesc;
    }
}
