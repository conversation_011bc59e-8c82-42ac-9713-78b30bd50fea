package com.yxt.invoice.domain.model.valueobject;

import com.yxt.invoice.domain.model.InvoiceMain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ExistsOrderInvoice {

    /**
     * 平台编码
     */
    @ApiModelProperty("平台编码")
    private String thirdPlatformCode;

    /**
     * 第三方平台订单号
     */
    @ApiModelProperty("三方平台订单号")
    private String thirdOrderNo;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    private String orderNo;

    /**
     * pos销售单号
     */
    @ApiModelProperty("pos销售单号")
    private String posNo;

    /**
     * 交易场景 online:线上交易, offline:线下交易
     */
    @ApiModelProperty("交易场景 online:线上交易, offline:线下交易")
    private String transactionChannel;

    /**
     * 业务类型 O2O, B2C
     */
    @ApiModelProperty("业务类型 O2O、B2C ,  线下单填O2O")
    private String businessType;

    /**
     * 是否已开票
     */
    @ApiModelProperty("发票信息 已申请时有值")
    private InvoiceMain invoiceMain;

    @ApiModelProperty("发票开具金额选项 未申请时有值")
    private List<InvoiceAmount> invoiceAmounts;


}
