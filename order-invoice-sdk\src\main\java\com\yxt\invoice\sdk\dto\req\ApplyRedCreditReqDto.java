package com.yxt.invoice.sdk.dto.req;

import com.yxt.lang.dto.api.MiddleRequestBase;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 发票红冲请求
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Data
public class ApplyRedCreditReqDto extends MiddleRequestBase implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 开票单号
     */
    @NotEmpty(message = "发票信息不能为空")
    @Valid
    private String invoiceMainNo;


    /**
     * 申请开票时间
     */
    @NotEmpty(message = "发票信息不能为空")
    @Valid
    private Date applyTime;


    /**
     * 红冲原因
     * 01: 开票有误
     * 02: 销货退回
     * 03: 服务中止
     * 04: 销售折让
     */
    private String redCreditReason;


    /**
     * 备注
     */
    private String notes;

    /**
     * 操作人用户ID
     */
    private String operatorUserId;



    /**
     * 订单号
     */
    private String orderNo;


}
