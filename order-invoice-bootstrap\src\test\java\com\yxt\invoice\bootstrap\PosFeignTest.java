package com.yxt.invoice.bootstrap;

import com.yxt.invoice.infrastructure.provider.dto.pos.PosInvoiceQueryData;
import com.yxt.invoice.infrastructure.provider.dto.pos.PosInvoiceQueryReq;
import com.yxt.invoice.infrastructure.provider.dto.pos.PosResponse;
import com.yxt.invoice.infrastructure.provider.feign.HdPosFeign;
import javax.annotation.Resource;
import org.junit.Test;

public class PosFeignTest extends BaseTest{

  @Resource
  private HdPosFeign hdPosFeign;


  @Test
  public void test1(){
    PosInvoiceQueryReq req = new PosInvoiceQueryReq();
    req.setThirdOrderNo("1125081600011140");
    req.setStoreCode("F005");

    PosResponse<PosInvoiceQueryData> posInvoiceQueryDataPosResponse = hdPosFeign.invoiceQuery(req);
    Boolean success = posInvoiceQueryDataPosResponse.success();
    System.out.println();
  }
}
