package com.yxt.invoice.sdk.dto.res;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 */
@Data
public class ApplyInvoiceMainResDto {

     @ApiModelProperty(value = "返回编码 1000=success other fail")
     private String  code;

    /**
     * 商户编码
     */
    @ApiModelProperty("商户编码")
    private String merCode;
    /**
     * 平台编码
     */
    @ApiModelProperty("平台编码")
    private String thirdPlatformCode;

    /**
     * 第三方平台订单号
     */
    @ApiModelProperty("三方平台订单号")
    private String thirdOrderNo;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    private String orderNo;

    /**
     * pos销售单号
     */
    @ApiModelProperty("pos销售单号")
    private String posNo;

    /**
     * 开票单号
     */
    @ApiModelProperty(value = "开票单号")
    private String invoiceMainNo;

    /**
     * 发票号码（税务云回调写入）
     */
    @ApiModelProperty(value = "发票号码")
    private String invoiceNo;


    /**
     * 交易场景 online:线上交易, offline:线下交易
     */
    @ApiModelProperty("交易场景 online:线上交易, offline:线下交易")
    private String transactionChannel;
    /**
     * 会员编号
     */
    @ApiModelProperty("会员编号")
    private String userId;

    /**
     * 蓝票:Tax_Invoice 红票:Credit_Note
     */
    @ApiModelProperty("蓝票:Tax_Invoice 红票:Credit_Note")
    private String invoiceRedBlueType;



    /**
     * 发票状态
     */
    @ApiModelProperty("待开票:WAIT 开票中:PROCESS 失败:FAIL 成功:SUCCESS 红冲中:RED_PROCESS 红冲成功:RED_SUCCESS  ")
    private String invoiceStatus;

    /**
     * 电子发票PDF地址
     */
    @ApiModelProperty("电子发票PDF地址")
    private String pdfUrl;

    /**
     * 失败原因
     */
    @ApiModelProperty("失败原因")
    private String invoiceErrMsg;

    /**
     * 数据版本，每次update+1
     */
    @ApiModelProperty("数据版本，每次update+1")
    private Long version;

    public static ApplyInvoiceMainResDto fail() {
        ApplyInvoiceMainResDto res = new ApplyInvoiceMainResDto();
        res.setCode("1");
        res.setInvoiceErrMsg("失败");
        return res;
    }
}
