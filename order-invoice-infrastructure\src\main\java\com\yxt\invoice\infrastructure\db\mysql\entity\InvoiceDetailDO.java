package com.yxt.invoice.infrastructure.db.mysql.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 发票明细表DO
 * 与数据库表invoice_detail对应
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Data
@TableName("invoice_detail")
public class InvoiceDetailDO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 开票单号
     */
    @TableField("invoice_main_no")
    private String invoiceMainNo;

    /**
     * 开票明细单号
     */
    @TableField("invoice_detail_no")
    private String invoiceDetailNo;

    /**
     * 行号
     */
    @TableField("row_no")
    private String rowNo;

    /**
     * 税收分类编码
     */
    @TableField("tax_classification_code")
    private String taxClassificationCode;

    /**
     * 顶级税收分类编码
     */
    @TableField("top_level_tax_classification_code")
    private String topLevelTaxClassificationCode;

    /**
     * ERP商品编码
     */
    @TableField("erp_code")
    private String erpCode;

    /**
     * ERP商品名称
     */
    @TableField("erp_name")
    private String erpName;

    /**
     * 商品数量
     */
    @TableField("commodity_count")
    private BigDecimal commodityCount;

    /**
     * 规格型号
     */
    @TableField("commodity_spec")
    private String commoditySpec;

    /**
     * 单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 单价
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 金额
     */
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 税额
     */
    @TableField("tax_amount")
    private BigDecimal taxAmount;

    /**
     * 税率
     */
    @TableField("tax_rate")
    private BigDecimal taxRate;

    /**
     * 税率编码
     */
    @TableField("tax_rate_code")
    private String taxRateCode;

    /**
     * 价税合计
     */
    @TableField("price_tax_amount")
    private BigDecimal priceTaxAmount;

    /**
     * 发票行性质
     */
    @TableField("invoice_line_type")
    private String invoiceLineType;

    /**
     * 折扣金额
     */
    @TableField("discount_amount")
    private BigDecimal discountAmount;

    /**
     * 优惠政策标识
     */
    @TableField("policy_status")
    private String policyStatus;

    /**
     * 优惠政策标签
     */
    @TableField("policy_tag")
    private String policyTag;

    /**
     * 优惠政策税率
     */
    @TableField("policy_tax_rate")
    private BigDecimal policyTaxRate;

    /**
     * 是否有效
     */
    @TableField("is_valid")
    private Long isValid;

    /**
     * 创建时间
     */
    @TableField(value = "created", fill = FieldFill.INSERT)
    private LocalDateTime created;

    /**
     * 更新时间
     */
    @TableField(value = "updated", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updated;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 系统创建时间
     */
    @TableField("sys_create_time")
    private LocalDateTime sysCreateTime;

    /**
     * 系统更新时间
     */
    @TableField("sys_update_time")
    private LocalDateTime sysUpdateTime;

    /**
     * 数据版本
     */
    @Version
    @TableField("version")
    private Long version;
}
