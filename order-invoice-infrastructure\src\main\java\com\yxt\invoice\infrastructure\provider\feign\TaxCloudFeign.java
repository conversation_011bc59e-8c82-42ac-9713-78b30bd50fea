package com.yxt.invoice.infrastructure.provider.feign;


import com.yxt.invoice.infrastructure.provider.dto.req.GetInvoiceByResponseIdReqDto;
import com.yxt.invoice.infrastructure.provider.dto.req.PositiveInvoiceIssueReqDto;
import com.yxt.invoice.infrastructure.provider.dto.req.PostNegativeInvoiceIssueReqDto;
import com.yxt.invoice.infrastructure.provider.dto.res.GetInvoiceByResponseIdResDto;
import com.yxt.invoice.infrastructure.provider.dto.res.PositiveInvoiceIssueResDto;
import com.yxt.invoice.infrastructure.provider.dto.res.PostNegativeInvoiceIssueResDto;
import com.yxt.invoice.infrastructure.provider.dto.res.TaxCloudResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 税务云接口
 *
 * @author: moatkon
 * @time: 2024/12/12 14:43
 */
@FeignClient(value = "tax-cloud-service")
public interface TaxCloudFeign {

    @PostMapping("/positiveInvoiceIssue")
    TaxCloudResponse<List<PositiveInvoiceIssueResDto>> positiveInvoiceIssue(@RequestBody PositiveInvoiceIssueReqDto req);


    @PostMapping("/postNegativeInvoiceIssue")
    TaxCloudResponse<PostNegativeInvoiceIssueResDto> postNegativeInvoiceIssue(@RequestBody PostNegativeInvoiceIssueReqDto req);



    @PostMapping("/getInvoiceByResponseId")
    TaxCloudResponse<GetInvoiceByResponseIdResDto> getInvoiceByResponseId(@RequestBody GetInvoiceByResponseIdReqDto req);


}
