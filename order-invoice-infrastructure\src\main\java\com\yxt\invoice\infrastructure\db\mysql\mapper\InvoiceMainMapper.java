package com.yxt.invoice.infrastructure.db.mysql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.invoice.infrastructure.db.mysql.entity.InvoiceMainDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 发票主表Mapper接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Mapper
public interface InvoiceMainMapper extends BaseMapper<InvoiceMainDO> {
  Long selectMaxId(@Param("flashParam") FlashParam flashParam);

  Long selectMinId(@Param("flashParam") FlashParam flashParam);
}
