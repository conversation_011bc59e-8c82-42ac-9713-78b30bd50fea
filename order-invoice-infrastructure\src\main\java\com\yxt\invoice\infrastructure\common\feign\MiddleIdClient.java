package com.yxt.invoice.infrastructure.common.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月03日 13:53
 * @email: <EMAIL>
 */
@FeignClient(value = "middle-id")
public interface MiddleIdClient {

  @GetMapping("/1.0/id/_get")
  List<Long> getId(@RequestParam(name = "batch", defaultValue = "1")
  int batch);
}
