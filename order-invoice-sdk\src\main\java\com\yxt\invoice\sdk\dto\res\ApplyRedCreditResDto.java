package com.yxt.invoice.sdk.dto.res;

import com.yxt.lang.dto.api.ResponseDTO;
import com.yxt.order.types.invoice.enums.InvoiceStatusEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 发票红冲响应
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Data
public class ApplyRedCreditResDto extends ResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 开票单号
     */
    private String invoiceMainNo;

    /**
     * 红冲是否成功
     */
    private String invoiceStatus;

    /**
     * 消息
     */
    private String message;

    public ApplyRedCreditResDto(String orderNo, String invoiceMainNo, InvoiceStatusEnum invoiceStatus, String invoiceErrMsg) {
                    this.orderNo=orderNo;
                    this.invoiceMainNo=invoiceMainNo;
                    this.invoiceStatus=invoiceStatus.getCode();
                    this.message=invoiceErrMsg;


    }


    public String getInvoiceMainNo() {
        return invoiceMainNo;
    }

    public void setInvoiceMainNo(String invoiceMainNo) {
        this.invoiceMainNo = invoiceMainNo;
    }


    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getInvoiceStatus() {
        return invoiceStatus;
    }

    public void setInvoiceStatus(String invoiceStatus) {
        this.invoiceStatus = invoiceStatus;
    }


}
