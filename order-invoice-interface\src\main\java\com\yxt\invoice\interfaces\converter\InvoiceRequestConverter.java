package com.yxt.invoice.interfaces.converter;

import com.google.common.base.Preconditions;
import com.yxt.invoice.domain.command.*;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.valueobject.InvoiceAmount;
import com.yxt.invoice.interfaces.common.UserContext;
import com.yxt.invoice.sdk.dto.InvoiceAmountDTO;
import com.yxt.invoice.sdk.dto.req.*;
import com.yxt.order.types.invoice.enums.InvoiceBuyerPartyTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceTypeEnum;
import com.yxt.order.types.offline.OfflineOrderNo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 发票请求转换器
 * 负责将SDK请求对象转换为Domain Command或Application Query
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Slf4j
public class InvoiceRequestConverter {



    /**
     * 转换申请开票请求为命令
     */
    public static ApplyInvoiceCommand convertToApplyInvoiceCommand(ApplyInvoiceReqDto request) {
        log.debug("开始转换申请开票请求为命令");

        if (request == null) {
            throw new IllegalArgumentException("申请开票请求不能为空");
        }


        String currentUser = UserContext.getCurrentUserId();

        ApplyInvoiceCommand command = new ApplyInvoiceCommand();
        // 传递MiddleRequestBase基类属性
        command.setBizCode(request.getBizCode());
        command.setScene(request.getScene());
        command.setChannel(request.getChannel());
        command.setDevice(request.getDevice());
        // 转换DTO为Domain对象
        command.setInvoiceMain(InvoiceDTOConverter.convertToInvoiceMain(request.getInvoiceMain()));
        command.setDetails(InvoiceDTOConverter.convertToInvoiceDetails(request.getDetails()));



        log.debug("申请开票请求转换完成，订单号：{}", command.getInvoiceMain().getOrderNo());
        return command;
    }

    /**
     * 转换红冲请求为命令
     */
    public static RedCreditInvoiceCommand convertToRedCreditCommand(ApplyRedCreditReqDto request) {
        log.debug("开始转换红冲请求为命令");

        if (request == null) {
            throw new IllegalArgumentException("红冲请求不能为空");
        }


        String currentUser = UserContext.getCurrentUserId();

        RedCreditInvoiceCommand command = new RedCreditInvoiceCommand();

        // 传递MiddleRequestBase基类属性
        command.setBizCode(request.getBizCode());
        command.setScene(request.getScene());
        command.setChannel(request.getChannel());
        command.setDevice(request.getDevice());

        command.setInvoiceMainNo(request.getInvoiceMainNo());
        command.setRedCreditReason(request.getRedCreditReason());
        command.setNotes(request.getNotes());
        command.setOperatorUserId(currentUser);
        command.setApplyTime(request.getApplyTime());


        log.debug("红冲请求转换完成，开票单号：{}", command.getInvoiceMainNo());
        return command;
    }

    /**
     * 转换发票列表请求为查询对象
     */
    public static QueryInvoiceListCommand convertToInvoiceListQuery(InvoiceListReqDto request) {
        log.debug("开始转换发票列表请求为查询对象");

        if (request == null) {
            throw new IllegalArgumentException("查询请求不能为空");
        }

        QueryInvoiceListCommand command = new QueryInvoiceListCommand();

        // 传递MiddleRequestBase基类属性
        command.setBizCode(request.getBizCode());
        command.setScene(request.getScene());
        command.setChannel(request.getChannel());
        command.setDevice(request.getDevice());

        // 基本查询条件
        command.setCompanyCode(request.getCompanyCode());
        command.setOrganizationCode(request.getOrganizationCode());
        command.setOrderNo(request.getOrderNo());
        command.setPosNo(request.getPosNo());
        command.setInvoiceMainNo(request.getInvoiceMainNo());
        command.setInvoiceStatus(request.getInvoiceStatus());
        command.setInvoiceType(request.getInvoiceType());
        command.setBuyerPartyType(request.getBuyerPartyType());

        // 分页参数处理
        if (request.getCurrentPage() != null && request.getCurrentPage() > 0) {
            command.setCurrentPage(request.getCurrentPage().longValue());
        } else {
            command.setCurrentPage(1L);
        }

        if (request.getPageSize() != null && request.getPageSize() > 0) {
            command.setPageSize(Math.min(request.getPageSize().longValue(), 100L));
        } else {
            command.setPageSize(20L);
        }

        log.debug("发票列表请求转换完成，查询条件：机构={}, 用户={}, 分页={}/{}",
                command.getOrganizationCode(), command.getUserId(),
                command.getCurrentPage(), command.getPageSize());

        return command;
    }

    /**
     * 转换发票详情请求为查询对象
     */
    public static QueryInvoiceDetailCommand convertToInvoiceDetailCommand(InvoiceDetailReqDto request) {
        log.debug("开始转换发票详情请求为查询对象");

        if (request == null) {
            throw new IllegalArgumentException("详情查询请求不能为空");
        }
        QueryInvoiceDetailCommand command = new QueryInvoiceDetailCommand();
        // 传递MiddleRequestBase基类属性
        command.setBizCode(request.getBizCode());
        command.setScene(request.getScene());
        command.setChannel(request.getChannel());
        command.setDevice(request.getDevice());

        command.setInvoiceMainNo(request.getInvoiceMainNo());
        command.setOrderNo(request.getOrderNo());
        command.setPosNo(request.getPosNo());

        return command;
    }



    public static ApplyInvoiceCommand convertToSimpleApplyInvoiceCommand(SimpleApplyInvoiceMainReqDto reqDto) {
        ApplyInvoiceCommand command = new ApplyInvoiceCommand();

        InvoiceMain invoiceMain = new InvoiceMain();
        invoiceMain.setOrderNo(OfflineOrderNo.orderNo(reqDto.getOrderNo()));
        invoiceMain.setMerCode(reqDto.getMerCode());
        invoiceMain.setBusinessType(reqDto.getBusinessType());
        invoiceMain.setTransactionChannel(reqDto.getTransactionChannel());
        invoiceMain.setCreatedBy(reqDto.getOperatorUserId());
        invoiceMain.setUserId(reqDto.getUserId());
        invoiceMain.setInvoiceType(InvoiceTypeEnum.fromCode(reqDto.getInvoiceType()));
        invoiceMain.setNotes(reqDto.getNotes());
        invoiceMain.setApplyTime(reqDto.getApplyTime());
        invoiceMain.setApplyChannel(reqDto.getApplyChannel());
        invoiceMain.setBuyerPartyType(InvoiceBuyerPartyTypeEnum.fromCode(reqDto.getBuyerPartyType()));
        invoiceMain.setBuyerName(reqDto.getBuyerName());
        invoiceMain.setBuyerTin(reqDto.getBuyerTin());
        invoiceMain.setBuyerAddress(reqDto.getBuyerAddress());
        invoiceMain.setBuyerPhone(reqDto.getBuyerPhone());
        invoiceMain.setBuyerBank(reqDto.getBuyerBank());
        invoiceMain.setBuyerBankAccount(reqDto.getBuyerBankAccount());
        invoiceMain.setBuyerEmail(reqDto.getBuyerEmail());
        invoiceMain.setBuyerMobile(reqDto.getBuyerMobile());
        invoiceMain.setShowBuyerBankAccount(reqDto.getShowBuyerBankAccount());
        command.setInvoiceMain(invoiceMain);
        command.setInvoiceAmount(convertToInvoiceAmount(reqDto.getInvoiceAmount()));
        String currentUser = UserContext.getCurrentUserId();
        Preconditions.checkArgument(StringUtils.isNotEmpty(currentUser)&&currentUser.equals(reqDto.getOperatorUserId()), "当前用户ID与操作用户ID不一致");
        return command;
    }


    private static InvoiceAmount convertToInvoiceAmount(InvoiceAmountDTO reqDto) {
        return new InvoiceAmount(reqDto.getKey(), reqDto.getAmount());
    }
    public static RedCreditInvoiceCommand convertToSimpleRedCreditCommand(SimpleApplyRedInvoiceMainReqDto reqDto) {
        RedCreditInvoiceCommand command = new RedCreditInvoiceCommand();
        command.setInvoiceMainNo(reqDto.getRedInvoiceMainNo());
        command.setRedCreditReason(reqDto.getRedInvoiceReason());
        command.setNotes(reqDto.getNotes());
        command.setOperatorUserId(reqDto.getOperatorUserId());
        command.setApplyTime(reqDto.getApplyTime());
        return command;

    }

    public static ExistsOrderInvoiceCommand convertToExistsOrderInvoiceCommand(QueryOrderExistsInvoiceReqDto reqDto) {
        ExistsOrderInvoiceCommand command = new ExistsOrderInvoiceCommand();
        // 传递MiddleRequestBase基类属性
        command.setBizCode(reqDto.getBizCode());
        command.setScene(reqDto.getScene());
        command.setChannel(reqDto.getChannel());
        command.setDevice(reqDto.getDevice());

        command.setOrderNo(reqDto.getOrderNo());
        command.setTransactionChannel(reqDto.getTransactionChannel());
        command.setBusinessType(reqDto.getBusinessType());
        command.setPosNo(reqDto.getPosNo());
        return command;
    }

    public static ExistsThirdOrderInvoiceCommand convertToExistsThirdOrderInvoiceCommand(QueryThirdOrderExistsInvoiceReqDto reqDto) {
        ExistsThirdOrderInvoiceCommand command = new ExistsThirdOrderInvoiceCommand();
        // 传递MiddleRequestBase基类属性
        command.setBizCode(reqDto.getBizCode());
        command.setScene(reqDto.getScene());
        command.setChannel(reqDto.getChannel());
        command.setDevice(reqDto.getDevice());

        command.setThirdOrderNo(reqDto.getThirdOrderNo());
        return command;
    }


}
